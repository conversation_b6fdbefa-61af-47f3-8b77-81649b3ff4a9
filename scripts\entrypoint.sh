#!/bin/bash
# 大樂透分析系統啟動腳本

set -e

echo "🎯 啟動大樂透分析系統..."

# 設置字體配置
export MPLBACKEND=Agg
python /app/font_config.py

# 檢查數據目錄
if [ ! -d "/app/data" ]; then
    echo "創建數據目錄..."
    mkdir -p /app/data
fi

# 檢查日誌目錄
if [ ! -d "/app/logs" ]; then
    echo "創建日誌目錄..."
    mkdir -p /app/logs
fi

# 檢查輸出目錄
if [ ! -d "/app/output" ]; then
    echo "創建輸出目錄..."
    mkdir -p /app/output
fi

# 等待數據庫連接（如果配置了數據庫）
if [ ! -z "$DATABASE_URL" ]; then
    echo "等待數據庫連接..."
    python -c "
import time
import psycopg2
import os
from urllib.parse import urlparse

url = urlparse(os.environ['DATABASE_URL'])
max_retries = 30
retry_count = 0

while retry_count < max_retries:
    try:
        conn = psycopg2.connect(
            host=url.hostname,
            port=url.port,
            user=url.username,
            password=url.password,
            database=url.path[1:]
        )
        conn.close()
        print('數據庫連接成功!')
        break
    except psycopg2.OperationalError:
        retry_count += 1
        print(f'等待數據庫連接... ({retry_count}/{max_retries})')
        time.sleep(2)
else:
    print('數據庫連接失敗!')
    exit(1)
"
fi

# 等待Redis連接（如果配置了Redis）
if [ ! -z "$REDIS_URL" ]; then
    echo "等待Redis連接..."
    python -c "
import time
import redis
import os
from urllib.parse import urlparse

url = urlparse(os.environ['REDIS_URL'])
max_retries = 30
retry_count = 0

while retry_count < max_retries:
    try:
        r = redis.Redis(
            host=url.hostname,
            port=url.port,
            password=url.password
        )
        r.ping()
        print('Redis連接成功!')
        break
    except redis.ConnectionError:
        retry_count += 1
        print(f'等待Redis連接... ({retry_count}/{max_retries})')
        time.sleep(2)
else:
    print('Redis連接失敗!')
    exit(1)
"
fi

# 運行數據庫遷移（如果需要）
if [ "$RUN_MIGRATIONS" = "true" ]; then
    echo "運行數據庫遷移..."
    python scripts/migrate.py
fi

# 初始化數據（如果需要）
if [ "$INIT_DATA" = "true" ]; then
    echo "初始化示例數據..."
    python scripts/init_data.py
fi

# 設置日誌級別
export LOG_LEVEL=${LOG_LEVEL:-INFO}

echo "✅ 系統初始化完成!"
echo "📊 開始運行大樂透分析系統..."

# 執行傳入的命令
exec "$@"
