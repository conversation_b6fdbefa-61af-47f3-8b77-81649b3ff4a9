#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
樂透統計分析實戰示例
展示如何使用一般統計方法分析樂透數據
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

def create_realistic_lottery_data(periods=500):
    """創建更真實的樂透數據（模擬真實開獎特徵）"""
    np.random.seed(123)
    data = []
    
    for i in range(periods):
        # 使用稍微不均勻的機率來模擬真實情況
        weights = np.random.uniform(0.8, 1.2, 49)  # 輕微的不均勻性
        weights = weights / weights.sum()
        
        # 生成6個不重複的號碼
        numbers = sorted(np.random.choice(range(1, 50), size=6, replace=False, p=weights))
        
        data.append({
            'period': i + 1,
            'date': f'2024-{(i//30)+1:02d}-{(i%30)+1:02d}',
            'numbers': numbers
        })
    
    return pd.DataFrame(data)

def comprehensive_statistical_analysis():
    """綜合統計分析示例"""
    
    print("🎲 樂透統計分析實戰示例")
    print("="*50)
    
    # 1. 載入數據
    print("\n📊 步驟1: 載入和預處理數據")
    data = create_realistic_lottery_data(500)
    print(f"✅ 已載入 {len(data)} 期樂透數據")
    
    # 2. 基礎統計分析
    print("\n📈 步驟2: 基礎統計分析")
    basic_stats = perform_basic_statistics(data)
    
    # 3. 機率分佈檢驗
    print("\n🎯 步驟3: 機率分佈檢驗")
    distribution_tests = perform_distribution_tests(data)
    
    # 4. 頻率分析
    print("\n📊 步驟4: 頻率分析")
    frequency_analysis = perform_frequency_analysis(data)
    
    # 5. 模式分析
    print("\n🎨 步驟5: 模式分析")
    pattern_analysis = perform_pattern_analysis(data)
    
    # 6. 高級統計分析
    print("\n🚀 步驟6: 高級統計分析")
    advanced_analysis = perform_advanced_analysis(data)
    
    # 7. 生成可視化報告
    print("\n📋 步驟7: 生成可視化報告")
    generate_visualization_report(data, frequency_analysis)
    
    # 8. 結論和建議
    print("\n💡 步驟8: 分析結論")
    generate_conclusions(basic_stats, distribution_tests, frequency_analysis)

def perform_basic_statistics(data):
    """執行基礎統計分析"""
    
    # 提取所有號碼
    all_numbers = [num for numbers in data['numbers'] for num in numbers]
    
    # 計算基本統計量
    stats_summary = {
        'total_numbers': len(all_numbers),
        'unique_numbers': len(set(all_numbers)),
        'mean_number': np.mean(all_numbers),
        'std_number': np.std(all_numbers),
        'median_number': np.median(all_numbers)
    }
    
    # 和值統計
    sum_values = [sum(numbers) for numbers in data['numbers']]
    stats_summary.update({
        'mean_sum': np.mean(sum_values),
        'std_sum': np.std(sum_values),
        'min_sum': min(sum_values),
        'max_sum': max(sum_values)
    })
    
    print(f"總號碼數: {stats_summary['total_numbers']}")
    print(f"號碼平均值: {stats_summary['mean_number']:.2f}")
    print(f"和值平均值: {stats_summary['mean_sum']:.2f} (理論值: 150)")
    print(f"和值標準差: {stats_summary['std_sum']:.2f}")
    
    return stats_summary

def perform_distribution_tests(data):
    """執行機率分佈檢驗"""
    
    results = {}
    
    # 1. 均勻分佈檢驗
    all_numbers = [num for numbers in data['numbers'] for num in numbers]
    observed_freq = [all_numbers.count(i) for i in range(1, 50)]
    expected_freq = len(all_numbers) / 49
    
    chi2_stat, p_value = stats.chisquare(observed_freq)
    results['uniformity'] = {
        'chi2_statistic': chi2_stat,
        'p_value': p_value,
        'is_uniform': p_value > 0.05
    }
    
    print(f"均勻分佈檢驗:")
    print(f"  卡方統計量: {chi2_stat:.4f}")
    print(f"  p值: {p_value:.6f}")
    print(f"  結論: {'符合均勻分佈' if p_value > 0.05 else '不符合均勻分佈'}")
    
    # 2. 正態分佈檢驗（和值）
    sum_values = [sum(numbers) for numbers in data['numbers']]
    shapiro_stat, shapiro_p = stats.shapiro(sum_values)
    
    results['normality'] = {
        'shapiro_statistic': shapiro_stat,
        'p_value': shapiro_p,
        'is_normal': shapiro_p > 0.05
    }
    
    print(f"\n和值正態分佈檢驗:")
    print(f"  Shapiro-Wilk統計量: {shapiro_stat:.4f}")
    print(f"  p值: {shapiro_p:.6f}")
    print(f"  結論: {'符合正態分佈' if shapiro_p > 0.05 else '不符合正態分佈'}")
    
    # 3. 獨立性檢驗
    if len(sum_values) > 1:
        current_sums = sum_values[1:]
        previous_sums = sum_values[:-1]
        correlation, corr_p = stats.pearsonr(current_sums, previous_sums)
        
        results['independence'] = {
            'correlation': correlation,
            'p_value': corr_p,
            'is_independent': abs(correlation) < 0.1 and corr_p > 0.05
        }
        
        print(f"\n期間獨立性檢驗:")
        print(f"  相關係數: {correlation:.6f}")
        print(f"  p值: {corr_p:.6f}")
        print(f"  結論: {'期間獨立' if abs(correlation) < 0.1 else '存在相關性'}")
    
    return results

def perform_frequency_analysis(data):
    """執行頻率分析"""
    
    # 計算每個號碼的出現頻率
    all_numbers = [num for numbers in data['numbers'] for num in numbers]
    frequency_counter = Counter(all_numbers)
    
    # 統計指標
    frequencies = [frequency_counter.get(i, 0) for i in range(1, 50)]
    mean_freq = np.mean(frequencies)
    std_freq = np.std(frequencies)
    cv = std_freq / mean_freq
    
    # 分類號碼
    hot_threshold = mean_freq + std_freq
    cold_threshold = mean_freq - std_freq
    
    hot_numbers = [i for i in range(1, 50) if frequencies[i-1] > hot_threshold]
    cold_numbers = [i for i in range(1, 50) if frequencies[i-1] < cold_threshold]
    
    results = {
        'frequencies': frequencies,
        'mean_frequency': mean_freq,
        'std_frequency': std_freq,
        'cv': cv,
        'hot_numbers': hot_numbers,
        'cold_numbers': cold_numbers
    }
    
    print(f"頻率統計:")
    print(f"  平均頻率: {mean_freq:.2f}")
    print(f"  標準差: {std_freq:.2f}")
    print(f"  變異係數: {cv:.4f}")
    print(f"  熱門號碼: {hot_numbers}")
    print(f"  冷門號碼: {cold_numbers}")
    
    return results

def perform_pattern_analysis(data):
    """執行模式分析"""
    
    results = {}
    
    # 1. 奇偶分析
    odd_counts = []
    for numbers in data['numbers']:
        odd_count = sum(1 for n in numbers if n % 2 == 1)
        odd_counts.append(odd_count)
    
    odd_distribution = Counter(odd_counts)
    results['odd_even'] = odd_distribution
    
    print(f"奇偶分佈:")
    for odd_count, freq in sorted(odd_distribution.items()):
        percentage = freq / len(data) * 100
        print(f"  {odd_count}奇{6-odd_count}偶: {freq}次 ({percentage:.1f}%)")
    
    # 2. 連號分析
    consecutive_counts = []
    for numbers in data['numbers']:
        sorted_numbers = sorted(numbers)
        consecutive = 0
        max_consecutive = 0
        
        for i in range(1, len(sorted_numbers)):
            if sorted_numbers[i] == sorted_numbers[i-1] + 1:
                consecutive += 1
                max_consecutive = max(max_consecutive, consecutive + 1)
            else:
                consecutive = 0
        
        consecutive_counts.append(max_consecutive)
    
    consecutive_distribution = Counter(consecutive_counts)
    results['consecutive'] = consecutive_distribution
    
    print(f"\n連號分析:")
    for count, freq in sorted(consecutive_distribution.items()):
        percentage = freq / len(data) * 100
        print(f"  {count}個連號: {freq}次 ({percentage:.1f}%)")
    
    # 3. 和值範圍分析
    sum_values = [sum(numbers) for numbers in data['numbers']]
    sum_ranges = {}
    for s in sum_values:
        range_key = (s // 20) * 20
        sum_ranges[range_key] = sum_ranges.get(range_key, 0) + 1
    
    results['sum_ranges'] = sum_ranges
    
    print(f"\n和值範圍分析:")
    for range_start, freq in sorted(sum_ranges.items()):
        percentage = freq / len(data) * 100
        print(f"  {range_start}-{range_start+19}: {freq}次 ({percentage:.1f}%)")
    
    return results

def perform_advanced_analysis(data):
    """執行高級統計分析"""
    
    results = {}
    
    # 1. 間隔分析
    print(f"間隔分析示例 (號碼1-10):")
    for number in range(1, 11):
        appearances = []
        for idx, numbers in enumerate(data['numbers']):
            if number in numbers:
                appearances.append(idx)
        
        if len(appearances) > 1:
            intervals = [appearances[i+1] - appearances[i] 
                        for i in range(len(appearances)-1)]
            avg_interval = np.mean(intervals)
            current_interval = len(data) - 1 - appearances[-1]
            
            print(f"  號碼{number:2d}: 平均間隔{avg_interval:5.1f}期, 當前間隔{current_interval:3d}期")
    
    # 2. 遊程檢驗示例
    print(f"\n遊程檢驗示例 (檢驗號碼1的隨機性):")
    number_1_sequence = [1 if 1 in numbers else 0 for numbers in data['numbers']]
    
    runs = 1
    for i in range(1, len(number_1_sequence)):
        if number_1_sequence[i] != number_1_sequence[i-1]:
            runs += 1
    
    n1 = number_1_sequence.count(0)
    n2 = number_1_sequence.count(1)
    expected_runs = (2 * n1 * n2) / (n1 + n2) + 1
    
    print(f"  實際遊程數: {runs}")
    print(f"  期望遊程數: {expected_runs:.2f}")
    print(f"  結論: {'符合隨機性' if abs(runs - expected_runs) < 10 else '可能非隨機'}")
    
    return results

def generate_visualization_report(data, frequency_analysis):
    """生成可視化報告"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 號碼頻率分佈
    axes[0, 0].bar(range(1, 50), frequency_analysis['frequencies'])
    axes[0, 0].axhline(y=frequency_analysis['mean_frequency'], color='r', linestyle='--', 
                       label=f'平均頻率: {frequency_analysis["mean_frequency"]:.1f}')
    axes[0, 0].set_title('號碼出現頻率分佈')
    axes[0, 0].set_xlabel('號碼')
    axes[0, 0].set_ylabel('出現次數')
    axes[0, 0].legend()
    
    # 2. 和值分佈
    sum_values = [sum(numbers) for numbers in data['numbers']]
    axes[0, 1].hist(sum_values, bins=30, alpha=0.7, density=True)
    
    # 添加正態分佈曲線
    mu, sigma = np.mean(sum_values), np.std(sum_values)
    x = np.linspace(min(sum_values), max(sum_values), 100)
    y = stats.norm.pdf(x, mu, sigma)
    axes[0, 1].plot(x, y, 'r-', label=f'正態分佈 (μ={mu:.1f}, σ={sigma:.1f})')
    axes[0, 1].axvline(x=150, color='g', linestyle='--', label='理論均值: 150')
    axes[0, 1].set_title('和值分佈')
    axes[0, 1].set_xlabel('和值')
    axes[0, 1].set_ylabel('密度')
    axes[0, 1].legend()
    
    # 3. 奇偶分佈
    odd_counts = [sum(1 for n in numbers if n % 2 == 1) for numbers in data['numbers']]
    odd_distribution = Counter(odd_counts)
    
    axes[1, 0].bar(odd_distribution.keys(), odd_distribution.values())
    axes[1, 0].set_title('奇偶分佈')
    axes[1, 0].set_xlabel('奇數個數')
    axes[1, 0].set_ylabel('出現次數')
    
    # 4. 頻率變異係數趨勢
    cv_trend = []
    window = 50
    for i in range(window, len(data)):
        subset_data = data.iloc[i-window:i]
        subset_numbers = [num for numbers in subset_data['numbers'] for num in numbers]
        subset_freq = [subset_numbers.count(j) for j in range(1, 50)]
        cv = np.std(subset_freq) / np.mean(subset_freq)
        cv_trend.append(cv)
    
    axes[1, 1].plot(range(window, len(data)), cv_trend)
    axes[1, 1].axhline(y=0.1, color='r', linestyle='--', label='理想CV值: 0.1')
    axes[1, 1].set_title('頻率變異係數趨勢')
    axes[1, 1].set_xlabel('期數')
    axes[1, 1].set_ylabel('變異係數')
    axes[1, 1].legend()
    
    plt.tight_layout()
    plt.savefig('樂透統計分析報告.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 可視化報告已生成並保存為 '樂透統計分析報告.png'")

def generate_conclusions(basic_stats, distribution_tests, frequency_analysis):
    """生成分析結論"""
    
    print("\n" + "="*50)
    print("📋 統計分析結論")
    print("="*50)
    
    print(f"\n【數據特徵】")
    print(f"• 分析了 {basic_stats['total_numbers']} 個號碼")
    print(f"• 號碼平均值: {basic_stats['mean_number']:.2f} (理論值: 25)")
    print(f"• 和值平均值: {basic_stats['mean_sum']:.2f} (理論值: 150)")
    
    print(f"\n【隨機性檢驗】")
    if distribution_tests['uniformity']['is_uniform']:
        print("✅ 號碼分佈符合均勻分佈假設")
    else:
        print("❌ 號碼分佈偏離均勻分佈")
    
    if 'independence' in distribution_tests and distribution_tests['independence']['is_independent']:
        print("✅ 各期開獎結果相互獨立")
    else:
        print("⚠️  期間可能存在微弱相關性")
    
    print(f"\n【頻率特徵】")
    cv = frequency_analysis['cv']
    if cv < 0.15:
        print(f"✅ 頻率變異係數 {cv:.4f} 表示分佈相對均勻")
    else:
        print(f"⚠️  頻率變異係數 {cv:.4f} 表示分佈不夠均勻")
    
    print(f"• 熱門號碼數量: {len(frequency_analysis['hot_numbers'])}")
    print(f"• 冷門號碼數量: {len(frequency_analysis['cold_numbers'])}")
    
    print(f"\n【統計學意義】")
    print("• 數據基本符合隨機性假設")
    print("• 觀察到的模式主要是隨機波動")
    print("• 統計分析無法提高中獎機率")
    
    print(f"\n【實用建議】")
    print("1. 樂透本質上是隨機事件")
    print("2. 過去結果不影響未來結果")
    print("3. 統計分析僅供學術研究")
    print("4. 請理性參與，量力而為")
    
    print(f"\n⚠️  重要提醒: 本分析僅供統計學習，不構成投注建議！")

if __name__ == "__main__":
    # 執行綜合統計分析
    comprehensive_statistical_analysis()
    
    print(f"\n🎉 樂透統計分析實戰示例完成！")
    print("您已經學會了如何使用一般統計方法分析樂透數據。")
    print("記住：統計分析的價值在於理解數據特徵，而非預測未來結果。")
