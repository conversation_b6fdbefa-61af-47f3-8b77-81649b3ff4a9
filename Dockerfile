# 大樂透分析系統 Dockerfile
FROM python:3.8-slim

# 設置維護者信息
LABEL maintainer="lottery-analyzer-team"
LABEL description="大樂透統計分析系統"
LABEL version="1.0.0"

# 設置環境變量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    MPLBACKEND=Agg \
    TZ=Asia/Taipei

# 設置工作目錄
WORKDIR /app

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    build-essential \
    python3-tk \
    fonts-wqy-zenhei \
    fonts-wqy-microhei \
    fontconfig \
    curl \
    wget \
    git \
    && fc-cache -fv \
    && rm -rf /var/lib/apt/lists/*

# 創建非root用戶
RUN groupadd -r lottery && useradd -r -g lottery lottery

# 複製requirements文件
COPY requirements.txt .

# 升級pip並安裝Python依賴
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 複製應用代碼
COPY . .

# 創建必要的目錄
RUN mkdir -p /app/data /app/logs /app/output /app/config && \
    chown -R lottery:lottery /app

# 設置字體配置
RUN echo "import matplotlib.pyplot as plt; plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'SimHei']; plt.rcParams['axes.unicode_minus'] = False" > /app/font_config.py

# 複製啟動腳本
COPY scripts/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# 切換到非root用戶
USER lottery

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD python -c "import 大樂透統計分析工具; print('OK')" || exit 1

# 暴露端口
EXPOSE 8000

# 設置入口點
ENTRYPOINT ["/entrypoint.sh"]

# 默認命令
CMD ["python", "大樂透統計分析工具.py"]
