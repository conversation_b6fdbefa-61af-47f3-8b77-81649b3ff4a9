#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平滑BIAS乖離率指標使用示例
展示如何使用20日和60日平滑移動平均線來分析BIAS指標
"""

import pandas as pd
import numpy as np
from smooth_bias_indicator import SmoothBiasIndicator

def main():
    """主函數 - 展示平滑BIAS指標的完整使用流程"""
    
    print("🎯 平滑BIAS乖離率指標分析系統")
    print("=" * 50)
    
    # 創建指標實例
    indicator = SmoothBiasIndicator()
    
    # 示例1: 使用默認設置
    print("\n📊 示例1: 使用默認設置 (20日和60日平滑線)")
    print("-" * 40)
    
    # 生成數據並分析
    indicator.generate_sample_data(days=150)
    indicator.print_analysis()
    
    # 示例2: 自定義參數設置
    print("\n📊 示例2: 自定義參數設置")
    print("-" * 40)
    
    # 創建新實例並自定義參數
    custom_indicator = SmoothBiasIndicator()
    custom_indicator.bias_period = 10  # 使用10日作為BIAS計算基準
    custom_indicator.smooth_periods = [15, 45]  # 使用15日和45日平滑線
    
    # 生成數據並分析
    custom_indicator.generate_sample_data(days=150)
    custom_indicator.calculate_smooth_bias()
    custom_indicator.calculate_bias_signals()
    
    print("自定義參數分析結果:")
    print(f"BIAS基準週期: {custom_indicator.bias_period}日")
    print(f"平滑週期: {custom_indicator.smooth_periods}")
    
    # 獲取狀態
    status = custom_indicator.get_current_status()
    if status:
        print(f"當前趨勢: {status['trend']}")
        print(f"趨勢強度: {status['strength']}")
        print(f"BIAS差值: {status['bias_diff']:.2f}%")
    
    # 示例3: 交易信號分析
    print("\n📊 示例3: 交易信號分析")
    print("-" * 40)
    
    signals = indicator.get_trading_signals()
    if signals:
        print(f"共發現 {len(signals)} 個交易信號:")
        for i, signal in enumerate(signals[-5:], 1):  # 顯示最近5個信號
            print(f"{i}. {signal[0].strftime('%Y-%m-%d')}: {signal[1]}")
            print(f"   詳情: {signal[2]}")
    else:
        print("暫無交易信號")
    
    # 示例4: 比較不同設置的效果
    print("\n📊 示例4: 比較不同平滑週期的效果")
    print("-" * 40)
    
    compare_different_settings()
    
    # 示例5: 實際應用建議
    print("\n📊 示例5: 實際應用建議")
    print("-" * 40)
    
    provide_usage_tips()
    
    print("\n✅ 示例演示完成！")
    print("💡 提示: 運行 smooth_bias_indicator.py 可以看到完整的圖表分析")

def compare_different_settings():
    """比較不同平滑週期設置的效果"""
    
    settings = [
        {'bias_period': 12, 'smooth_periods': [10, 30], 'name': '短期敏感型'},
        {'bias_period': 12, 'smooth_periods': [20, 60], 'name': '標準型'},
        {'bias_period': 12, 'smooth_periods': [30, 90], 'name': '長期穩定型'}
    ]
    
    # 使用相同的數據進行比較
    base_data = SmoothBiasIndicator()
    base_data.generate_sample_data(days=150)
    sample_data = base_data.data.copy()
    
    print("不同設置的比較結果:")
    print("-" * 50)
    
    for setting in settings:
        indicator = SmoothBiasIndicator()
        indicator.bias_period = setting['bias_period']
        indicator.smooth_periods = setting['smooth_periods']
        indicator.load_data(sample_data)
        
        indicator.calculate_smooth_bias()
        indicator.calculate_bias_signals()
        
        # 獲取信號數量
        signals = indicator.get_trading_signals()
        signal_count = len(signals)
        
        # 獲取當前狀態
        status = indicator.get_current_status()
        
        print(f"{setting['name']} ({setting['smooth_periods'][0]}日/{setting['smooth_periods'][1]}日):")
        print(f"  信號數量: {signal_count}")
        if status:
            print(f"  當前趨勢: {status['trend']}")
            print(f"  BIAS差值: {status['bias_diff']:.2f}%")
        print()

def provide_usage_tips():
    """提供使用建議"""
    
    print("💡 平滑BIAS指標使用建議:")
    print()
    
    print("🎯 參數選擇建議:")
    print("  • 短期交易 (1-2週): 使用 10日/30日 平滑線")
    print("  • 中期交易 (1-2月): 使用 20日/60日 平滑線 (推薦)")
    print("  • 長期投資 (3月+): 使用 30日/90日 平滑線")
    print()
    
    print("📈 信號解讀:")
    print("  • 金叉 (短線上穿長線): 潛在買入機會")
    print("  • 死叉 (短線下穿長線): 潛在賣出信號")
    print("  • 差值擴大: 趨勢加強")
    print("  • 差值收斂: 趨勢減弱，可能反轉")
    print()
    
    print("⚠️  注意事項:")
    print("  • 平滑處理會產生滯後，適合趨勢確認而非搶先交易")
    print("  • 在震盪市場中可能產生較多假信號")
    print("  • 建議結合其他技術指標一起使用")
    print("  • 回測驗證參數設置的有效性")
    print()
    
    print("🔧 優化建議:")
    print("  • 根據不同市場環境調整參數")
    print("  • 考慮加入成交量確認")
    print("  • 設置止損和止盈條件")
    print("  • 定期檢視和調整策略")

def create_custom_analysis():
    """創建自定義分析示例"""
    
    print("\n🔬 自定義分析示例:")
    print("-" * 30)
    
    # 創建指標
    indicator = SmoothBiasIndicator()
    indicator.generate_sample_data(days=200)
    indicator.calculate_smooth_bias()
    indicator.calculate_bias_signals()
    
    # 計算一些自定義指標
    data = indicator.data
    
    # 計算平滑BIAS的標準差 (波動性指標)
    short_period = indicator.smooth_periods[0]
    long_period = indicator.smooth_periods[1]
    
    short_bias_std = data[f'SmoothBIAS{short_period}'].rolling(30).std()
    long_bias_std = data[f'SmoothBIAS{long_period}'].rolling(30).std()
    
    print(f"最近30日平滑BIAS波動性:")
    print(f"  {short_period}日線標準差: {short_bias_std.iloc[-1]:.3f}")
    print(f"  {long_period}日線標準差: {long_bias_std.iloc[-1]:.3f}")
    
    # 計算信號勝率 (簡化版本)
    signals = indicator.get_trading_signals()
    if len(signals) >= 2:
        print(f"\n信號統計:")
        print(f"  總信號數: {len(signals)}")
        
        buy_signals = [s for s in signals if '買入' in s[1]]
        sell_signals = [s for s in signals if '賣出' in s[1]]
        
        print(f"  買入信號: {len(buy_signals)}")
        print(f"  賣出信號: {len(sell_signals)}")

if __name__ == "__main__":
    main()
    
    # 額外的自定義分析
    create_custom_analysis()
    
    print("\n" + "=" * 50)
    print("🎉 感謝使用平滑BIAS乖離率指標！")
    print("📊 記住：技術分析僅供參考，投資需謹慎！")
