import yfinance as yf
import pandas as pd
import matplotlib.pyplot as plt

# 解決matplotlib中文顯示問題
plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei'] 
plt.rcParams['axes.unicode_minus'] = False  # 解決負號顯示問題

# 參數設定
ticker = 'AAPL'          # 股票代碼（可改為你想分析的）
start_date = '2020-01-01'
end_date = '2024-01-01'
short_window = 20         # MA1：短期均線
long_window = 60         # MA2：長期均線

# 下載資料
df = yf.download(ticker, start=start_date, end=end_date)

# 檢查資料是否成功下載
if df is None or df.empty:
    print(f"無法下載 {ticker} 的資料，請檢查股票代碼或日期範圍。")
else:
    # 計算移動平均線
    df['MA_short'] = df['Close'].rolling(window=short_window).mean()
    df['MA_long'] = df['Close'].rolling(window=long_window).mean()

    # 計算雙線乖離（百分比）
    df['BIAS'] = (df['MA_short'] - df['MA_long']) / df['MA_long'] * 100

    # 畫圖：股價與均線
    plt.figure(figsize=(14, 6))
    plt.subplot(2, 1, 1)
    plt.plot(df['Close'], label='Close Price', color='black')
    plt.plot(df['MA_short'], label=f'MA{short_window}', color='blue')
    plt.plot(df['MA_long'], label=f'MA{long_window}', color='red')
    plt.title(f'{ticker} Price and Moving Averages')
    plt.legend()
    plt.grid(True)

    # 畫圖：雙線乖離
    plt.subplot(2, 1, 2)
    plt.plot(df['BIAS'], label='雙線乖離 (%)', color='purple')
    plt.axhline(0, color='gray', linestyle='--')
    plt.title('雙線乖離指標')
    plt.ylabel('乖離 (%)')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.show()
