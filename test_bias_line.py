#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試BIAS線條效果 - 展示BIAS值連接成線
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class SimpleBiasLine:
    """簡化的BIAS線條指標"""
    
    def __init__(self):
        self.data = None
        self.bias_periods = [6, 12, 24]
    
    def generate_sample_data(self, days=240):
        """生成模擬數據"""
        start_date = datetime.now() - timedelta(days=days)
        dates = [start_date + timedelta(days=i) for i in range(days)]
        
        np.random.seed(42)
        base_price = 100
        trend = np.sin(np.linspace(0, 4*np.pi, days)) * 5
        long_trend = np.linspace(0, 15, days)
        noise = np.random.normal(0, 1.5, days)
        prices = base_price + trend + long_trend + noise
        prices = np.maximum(prices, 50)
        
        self.data = pd.DataFrame({
            'date': dates,
            'close': prices
        })
        return self.data
    
    def calculate_bias_lines(self):
        """計算BIAS線條"""
        for period in self.bias_periods:
            # 移動平均線
            self.data[f'MA{period}'] = self.data['close'].rolling(window=period).mean()
            # BIAS線
            self.data[f'BIAS{period}'] = ((self.data['close'] - self.data[f'MA{period}']) /
                                         self.data[f'MA{period}']) * 100
        return self.data
    
    def print_bias_lines_analysis(self):
        """打印BIAS線條分析"""
        latest = self.data.iloc[-1]
        
        print("=" * 50)
        print("BIAS乖離率線條分析")
        print("=" * 50)
        print(f"日期: {latest['date'].strftime('%Y-%m-%d')}")
        print(f"股價: {latest['close']:.2f}")
        print("-" * 50)
        
        for period in self.bias_periods:
            bias_value = latest[f'BIAS{period}']
            ma_value = latest[f'MA{period}']
            
            # 狀態判斷
            if bias_value > 5:
                status = "超買"
            elif bias_value < -5:
                status = "超賣"
            elif bias_value > 0:
                status = "正乖離"
            else:
                status = "負乖離"
            
            print(f"BIAS{period}線: {bias_value:+6.2f}% (MA{period}: {ma_value:6.2f}) - {status}")
        
        print("-" * 50)
        
        # 展示線條數據（最近10天）
        print("最近10天BIAS線條數據:")
        recent_data = self.data.tail(10)
        
        print("日期        ", end="")
        for period in self.bias_periods:
            print(f"BIAS{period:2d}  ", end="")
        print()
        
        for _, row in recent_data.iterrows():
            print(f"{row['date'].strftime('%m-%d')} ", end="")
            for period in self.bias_periods:
                print(f"{row[f'BIAS{period}']:+6.2f}  ", end="")
            print()
        
        print("=" * 50)
        
        # 線條特徵分析
        print("BIAS線條特徵分析:")
        for period in self.bias_periods:
            bias_series = self.data[f'BIAS{period}'].dropna()
            
            # 統計特徵
            mean_bias = bias_series.mean()
            std_bias = bias_series.std()
            max_bias = bias_series.max()
            min_bias = bias_series.min()
            
            # 趨勢分析
            recent_5 = bias_series.tail(5)
            if len(recent_5) >= 2:
                trend = "上升" if recent_5.iloc[-1] > recent_5.iloc[0] else "下降"
            else:
                trend = "無法判斷"
            
            print(f"  BIAS{period}線:")
            print(f"    平均值: {mean_bias:+6.2f}%")
            print(f"    標準差: {std_bias:6.2f}%")
            print(f"    範圍: {min_bias:+6.2f}% 到 {max_bias:+6.2f}%")
            print(f"    近期趨勢: {trend}")
            
            # 零軸穿越次數
            zero_crosses = 0
            for i in range(1, len(bias_series)):
                if (bias_series.iloc[i] > 0) != (bias_series.iloc[i-1] > 0):
                    zero_crosses += 1
            
            print(f"    零軸穿越: {zero_crosses}次")
            print()
        
        print("💡 每個BIAS值都連接成連續的線條，形成類似移動平均線的效果！")
        print("📈 這些線條可以幫助識別趨勢變化和超買超賣狀態。")

def main():
    """主測試函數"""
    print("🧪 BIAS線條效果測試")
    print("-" * 30)
    
    # 創建指標
    bias_line = SimpleBiasLine()
    
    # 生成數據
    print("生成測試數據...")
    bias_line.generate_sample_data(240)
    
    # 計算BIAS線
    print("計算BIAS線條...")
    bias_line.calculate_bias_lines()
    
    # 分析結果
    bias_line.print_bias_lines_analysis()
    
    print("\n✅ BIAS線條測試完成！")
    print("💡 提示: 運行 bias_line_indicator.py 可以看到完整的圖表效果")

if __name__ == "__main__":
    main()
