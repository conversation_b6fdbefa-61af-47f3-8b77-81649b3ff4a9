import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SmoothBiasIndicator:
    """
    平滑BIAS乖離率指標
    將BIAS值進行移動平均處理，使用20日和60日兩條平滑線
    """
    
    def __init__(self):
        self.data = None
        self.bias_period = 12  # 計算BIAS的基準週期
        self.smooth_periods = [10, 60]  # 平滑移動平均週期
    
    def generate_sample_data(self, days=240):
        """生成模擬股價數據"""
        # 創建日期序列
        start_date = datetime.now() - timedelta(days=days)
        dates = [start_date + timedelta(days=i) for i in range(days)]
        
        # 生成模擬價格數據（帶有趨勢和隨機波動）
        np.random.seed(42)  # 固定隨機種子以便重現
        base_price = 100
        
        # 創建更複雜的價格模式
        trend = np.sin(np.linspace(0, 4*np.pi, days)) * 5  # 週期性波動
        long_trend = np.linspace(0, 15, days)  # 長期上升趨勢
        noise = np.random.normal(0, 1.5, days)  # 隨機波動
        
        prices = base_price + trend + long_trend + noise
        
        # 確保價格為正數
        prices = np.maximum(prices, 50)
        
        self.data = pd.DataFrame({
            'date': dates,
            'close': prices
        })
        
        return self.data
    
    def load_data(self, data):
        """載入外部數據"""
        self.data = data.copy()
        return self.data
    
    def calculate_moving_average(self, period):
        """計算移動平均線"""
        if self.data is None:
            raise ValueError("請先生成或載入數據")
        
        self.data[f'MA{period}'] = self.data['close'].rolling(window=period).mean()
        return self.data[f'MA{period}']
    
    def calculate_bias(self, period):
        """計算BIAS乖離率"""
        if self.data is None:
            raise ValueError("請先生成或載入數據")
        
        if f'MA{period}' not in self.data.columns:
            self.calculate_moving_average(period)
        
        # BIAS = (收盤價 - 移動平均線) / 移動平均線 * 100
        self.data[f'BIAS{period}'] = ((self.data['close'] - self.data[f'MA{period}']) /
                                      self.data[f'MA{period}']) * 100
        
        return self.data[f'BIAS{period}']
    
    def calculate_smooth_bias(self, bias_period=None, smooth_periods=None):
        """計算平滑BIAS指標"""
        if bias_period is None:
            bias_period = self.bias_period
        if smooth_periods is None:
            smooth_periods = self.smooth_periods

        # 首先計算基礎BIAS
        self.calculate_bias(bias_period)

        # 確保數據存在
        if self.data is None:
            raise ValueError("數據計算失敗，請檢查數據源")

        # 對BIAS值進行移動平均平滑處理
        for smooth_period in smooth_periods:
            column_name = f'SmoothBIAS{smooth_period}'
            self.data[column_name] = self.data[f'BIAS{bias_period}'].rolling(
                window=smooth_period).mean()

        return self.data
    
    def calculate_bias_signals(self):
        """計算BIAS信號線交叉"""
        if self.data is None:
            raise ValueError("請先生成或載入數據")

        if f'SmoothBIAS{self.smooth_periods[0]}' not in self.data.columns:
            self.calculate_smooth_bias()

        short_bias = self.data[f'SmoothBIAS{self.smooth_periods[0]}']
        long_bias = self.data[f'SmoothBIAS{self.smooth_periods[1]}']

        # 計算差值
        self.data['BIAS_Diff'] = short_bias - long_bias

        # 計算信號
        self.data['BIAS_Signal'] = 0

        # 金叉：短期線上穿長期線
        golden_cross = (self.data['BIAS_Diff'] > 0) & (self.data['BIAS_Diff'].shift(1) <= 0)
        self.data.loc[golden_cross, 'BIAS_Signal'] = 1

        # 死叉：短期線下穿長期線
        death_cross = (self.data['BIAS_Diff'] < 0) & (self.data['BIAS_Diff'].shift(1) >= 0)
        self.data.loc[death_cross, 'BIAS_Signal'] = -1

        return self.data
    
    def plot_smooth_bias_chart(self):
        """繪製平滑BIAS指標圖表"""
        if self.data is None:
            self.generate_sample_data()

        # 確保數據存在
        if self.data is None:
            raise ValueError("無法生成或載入數據")

        # 計算所有指標
        self.calculate_smooth_bias()
        self.calculate_bias_signals()
        
        # 創建子圖
        _, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(14, 12),
                                           gridspec_kw={'height_ratios': [2, 1.5, 1]})
        
        # 上圖：股價和移動平均線
        ax1.plot(self.data['date'], self.data['close'],
                label='股價', linewidth=2, color='black')
        ax1.plot(self.data['date'], self.data[f'MA{self.bias_period}'],
                label=f'{self.bias_period}日平均線', linewidth=2, color='blue', alpha=0.7)
        
        ax1.set_title(f'股價走勢與{self.bias_period}日移動平均線', fontsize=14, fontweight='bold')
        ax1.set_ylabel('價格', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 中圖：平滑BIAS指標
        short_period = self.smooth_periods[0]
        long_period = self.smooth_periods[1]
        
        ax2.plot(self.data['date'], self.data[f'SmoothBIAS{short_period}'],
                label=f'{short_period}日平滑BIAS', linewidth=2, color='red')
        ax2.plot(self.data['date'], self.data[f'SmoothBIAS{long_period}'],
                label=f'{long_period}日平滑BIAS', linewidth=2, color='blue')
        
        # 添加零軸線
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=1)
        
        # 添加超買超賣線
        ax2.axhline(y=5, color='red', linestyle='--', alpha=0.7, label='超買警戒線')
        ax2.axhline(y=-5, color='green', linestyle='--', alpha=0.7, label='超賣警戒線')
        
        # 填充區域
        ax2.fill_between(self.data['date'], 
                        self.data[f'SmoothBIAS{short_period}'], 
                        self.data[f'SmoothBIAS{long_period}'],
                        where=(self.data[f'SmoothBIAS{short_period}'] > self.data[f'SmoothBIAS{long_period}']),
                        alpha=0.3, color='red', label='多頭區域')
        ax2.fill_between(self.data['date'], 
                        self.data[f'SmoothBIAS{short_period}'], 
                        self.data[f'SmoothBIAS{long_period}'],
                        where=(self.data[f'SmoothBIAS{short_period}'] <= self.data[f'SmoothBIAS{long_period}']),
                        alpha=0.3, color='blue', label='空頭區域')
        
        ax2.set_title(f'平滑BIAS指標 ({short_period}日 vs {long_period}日)', fontsize=14, fontweight='bold')
        ax2.set_ylabel('BIAS值 (%)', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 下圖：BIAS差值和信號
        ax3.plot(self.data['date'], self.data['BIAS_Diff'],
                label='BIAS差值', linewidth=2, color='purple')
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=1)
        
        # 標記交叉信號
        buy_signals = self.data[self.data['BIAS_Signal'] == 1]
        sell_signals = self.data[self.data['BIAS_Signal'] == -1]
        
        if not buy_signals.empty:
            ax3.scatter(buy_signals['date'], buy_signals['BIAS_Diff'],
                       color='red', marker='^', s=100, label='金叉信號', zorder=5)
        
        if not sell_signals.empty:
            ax3.scatter(sell_signals['date'], sell_signals['BIAS_Diff'],
                       color='blue', marker='v', s=100, label='死叉信號', zorder=5)
        
        ax3.set_title('BIAS差值與交叉信號', fontsize=14, fontweight='bold')
        ax3.set_xlabel('日期', fontsize=12)
        ax3.set_ylabel('差值 (%)', fontsize=12)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def get_trading_signals(self):
        """獲取交易信號"""
        if self.data is None:
            raise ValueError("請先生成或載入數據")

        if 'BIAS_Signal' not in self.data.columns:
            self.calculate_bias_signals()

        signals = []
        signal_data = self.data[self.data['BIAS_Signal'] != 0]
        
        for _, row in signal_data.iterrows():
            date = row['date']
            signal_type = '買入信號' if row['BIAS_Signal'] == 1 else '賣出信號'
            short_bias = row[f'SmoothBIAS{self.smooth_periods[0]}']
            long_bias = row[f'SmoothBIAS{self.smooth_periods[1]}']
            
            description = f'{self.smooth_periods[0]}日BIAS: {short_bias:.2f}%, {self.smooth_periods[1]}日BIAS: {long_bias:.2f}%'
            signals.append((date, signal_type, description))
        
        return signals
    
    def print_analysis(self):
        """打印分析結果"""
        if self.data is None:
            self.generate_sample_data()

        # 確保數據存在
        if self.data is None:
            raise ValueError("無法生成或載入數據")

        # 計算所有指標
        self.calculate_smooth_bias()
        self.calculate_bias_signals()

        latest_data = self.data.iloc[-1]
        
        print("=" * 60)
        print("平滑BIAS乖離率分析報告")
        print("=" * 60)
        print(f"分析日期: {latest_data['date'].strftime('%Y-%m-%d')}")
        print(f"當前股價: {latest_data['close']:.2f}")
        print(f"{self.bias_period}日移動平均線: {latest_data[f'MA{self.bias_period}']:.2f}")
        print(f"原始BIAS{self.bias_period}值: {latest_data[f'BIAS{self.bias_period}']:.2f}%")
        print("-" * 60)
        
        # 平滑BIAS值
        short_period = self.smooth_periods[0]
        long_period = self.smooth_periods[1]
        
        short_bias = latest_data[f'SmoothBIAS{short_period}']
        long_bias = latest_data[f'SmoothBIAS{long_period}']
        bias_diff = latest_data['BIAS_Diff']
        
        print(f"{short_period}日平滑BIAS: {short_bias:.2f}%")
        print(f"{long_period}日平滑BIAS: {long_bias:.2f}%")
        print(f"BIAS差值: {bias_diff:.2f}%")
        
        # 判斷當前狀態
        if bias_diff > 1:
            trend_status = "多頭趨勢，短期平滑線在長期線之上"
        elif bias_diff < -1:
            trend_status = "空頭趨勢，短期平滑線在長期線之下"
        else:
            trend_status = "震盪整理，兩線接近"
        
        print(f"趨勢狀態: {trend_status}")
        
        # 強度判斷
        if abs(short_bias) > 5:
            strength = "強勢"
        elif abs(short_bias) > 2:
            strength = "中等"
        else:
            strength = "弱勢"
        
        print(f"趨勢強度: {strength}")
        
        # 顯示最近的交易信號
        signals = self.get_trading_signals()
        if signals:
            print(f"\n最近的交易信號:")
            for signal in signals[-3:]:  # 顯示最近3個信號
                print(f"  {signal[0].strftime('%Y-%m-%d')}: {signal[1]} - {signal[2]}")
        else:
            print("\n暫無交易信號")
        
        print("=" * 60)
    
    def get_current_status(self):
        """獲取當前狀態摘要"""
        if self.data is None or 'BIAS_Diff' not in self.data.columns:
            return None
        
        latest = self.data.iloc[-1]
        short_bias = latest[f'SmoothBIAS{self.smooth_periods[0]}']
        long_bias = latest[f'SmoothBIAS{self.smooth_periods[1]}']
        bias_diff = latest['BIAS_Diff']
        
        status = {
            'date': latest['date'],
            'price': latest['close'],
            'short_bias': short_bias,
            'long_bias': long_bias,
            'bias_diff': bias_diff,
            'trend': '多頭' if bias_diff > 0 else '空頭',
            'strength': '強' if abs(short_bias) > 5 else '中' if abs(short_bias) > 2 else '弱'
        }
        
        return status

# 使用示例
if __name__ == "__main__":
    # 創建平滑BIAS指標實例
    smooth_bias = SmoothBiasIndicator()
    
    # 生成示例數據
    print("正在生成模擬股價數據...")
    smooth_bias.generate_sample_data(days=240)
    
    # 打印分析報告
    smooth_bias.print_analysis()
    
    # 繪製圖表
    print("\n正在生成平滑BIAS指標圖表...")
    smooth_bias.plot_smooth_bias_chart()
    
    # 獲取當前狀態
    status = smooth_bias.get_current_status()
    if status:
        print(f"\n當前狀態摘要:")
        print(f"趨勢: {status['trend']} | 強度: {status['strength']} | 差值: {status['bias_diff']:.2f}%")
