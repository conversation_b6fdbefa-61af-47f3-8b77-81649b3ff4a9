# Prometheus配置文件
global:
  scrape_interval: 15s
  evaluation_interval: 15s

# 規則文件
rule_files:
  - "alert_rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身監控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 大樂透分析系統監控
  - job_name: 'lottery-analyzer'
    static_configs:
      - targets: ['lottery-analyzer:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # PostgreSQL監控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis監控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Node Exporter (系統監控)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']

  # Docker監控
  - job_name: 'docker'
    static_configs:
      - targets: ['cadvisor:8080']

  # Nginx監控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  # 應用程序自定義指標
  - job_name: 'lottery-app-metrics'
    static_configs:
      - targets: ['lottery-analyzer:8001']
    metrics_path: '/app-metrics'
    scrape_interval: 60s

# 遠程寫入配置（可選）
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     basic_auth:
#       username: "user"
#       password: "password"

# 遠程讀取配置（可選）
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint"
#     basic_auth:
#       username: "user"
#       password: "password"
