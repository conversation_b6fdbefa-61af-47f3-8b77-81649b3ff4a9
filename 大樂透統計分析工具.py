#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大樂透統計分析工具
採用傳統統計方法進行彩票數據分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency, kstest, normaltest
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class LotteryAnalyzer:
    """大樂透統計分析器"""
    
    def __init__(self):
        self.data = None
        self.numbers_data = None
        self.analysis_results = {}
    
    def load_data(self, data_source):
        """載入開獎數據"""
        if isinstance(data_source, str):
            # 從檔案載入
            self.data = pd.read_csv(data_source)
        else:
            # 直接使用DataFrame
            self.data = data_source.copy()
        
        # 數據預處理
        self._preprocess_data()
        print(f"已載入 {len(self.data)} 期開獎數據")
    
    def _preprocess_data(self):
        """數據預處理"""
        # 提取所有號碼（不包含特別號）
        numbers_cols = [col for col in self.data.columns if 'number' in col.lower() or '號碼' in col]
        if len(numbers_cols) >= 6:
            self.numbers_data = self.data[numbers_cols[:6]]
        else:
            # 假設前6列是主號碼
            self.numbers_data = self.data.iloc[:, :6]
    
    def frequency_analysis(self):
        """頻率分析"""
        print("\n=== 頻率分析 ===")
        
        # 計算每個號碼的出現頻率
        all_numbers = self.numbers_data.values.flatten()
        frequency = pd.Series(all_numbers).value_counts().sort_index()
        
        # 統計指標
        total_draws = len(self.data)
        expected_freq = total_draws * 6 / 49  # 理論期望頻率
        
        # 計算統計量
        mean_freq = frequency.mean()
        std_freq = frequency.std()
        cv_freq = std_freq / mean_freq  # 變異係數
        
        # 識別熱門和冷門號碼
        hot_threshold = mean_freq + std_freq
        cold_threshold = mean_freq - std_freq
        
        hot_numbers = frequency[frequency > hot_threshold].index.tolist()
        cold_numbers = frequency[frequency < cold_threshold].index.tolist()
        
        results = {
            'frequency': frequency,
            'mean_frequency': mean_freq,
            'std_frequency': std_freq,
            'cv_frequency': cv_freq,
            'expected_frequency': expected_freq,
            'hot_numbers': hot_numbers,
            'cold_numbers': cold_numbers
        }
        
        self.analysis_results['frequency'] = results
        
        print(f"平均出現頻率: {mean_freq:.2f}")
        print(f"標準差: {std_freq:.2f}")
        print(f"變異係數: {cv_freq:.3f}")
        print(f"熱門號碼 (>{hot_threshold:.1f}次): {hot_numbers}")
        print(f"冷門號碼 (<{cold_threshold:.1f}次): {cold_numbers}")
        
        return results
    
    def distribution_analysis(self):
        """分佈分析"""
        print("\n=== 分佈分析 ===")
        
        results = {}
        
        # 1. 奇偶分佈分析
        odd_even_dist = []
        for _, row in self.numbers_data.iterrows():
            numbers = row.values
            odd_count = sum(1 for n in numbers if n % 2 == 1)
            odd_even_dist.append(odd_count)
        
        odd_even_freq = pd.Series(odd_even_dist).value_counts().sort_index()
        results['odd_even_distribution'] = odd_even_freq
        
        # 2. 大小分佈分析 (1-25為小, 26-49為大)
        big_small_dist = []
        for _, row in self.numbers_data.iterrows():
            numbers = row.values
            big_count = sum(1 for n in numbers if n > 25)
            big_small_dist.append(big_count)
        
        big_small_freq = pd.Series(big_small_dist).value_counts().sort_index()
        results['big_small_distribution'] = big_small_freq
        
        # 3. 和值分析
        sum_values = self.numbers_data.sum(axis=1)
        results['sum_statistics'] = {
            'mean': sum_values.mean(),
            'std': sum_values.std(),
            'min': sum_values.min(),
            'max': sum_values.max(),
            'median': sum_values.median()
        }
        
        # 4. 區間分佈分析 (將1-49分為7個區間)
        interval_dist = []
        for _, row in self.numbers_data.iterrows():
            numbers = row.values
            intervals = [0] * 7  # 7個區間
            for n in numbers:
                interval_idx = min((n - 1) // 7, 6)  # 確保不超過索引範圍
                intervals[interval_idx] += 1
            interval_dist.append(intervals)
        
        interval_df = pd.DataFrame(interval_dist, 
                                 columns=[f'區間{i+1}' for i in range(7)])
        results['interval_distribution'] = interval_df.mean()
        
        self.analysis_results['distribution'] = results
        
        print(f"奇偶分佈: {dict(odd_even_freq)}")
        print(f"大小分佈: {dict(big_small_freq)}")
        print(f"和值統計: 平均={results['sum_statistics']['mean']:.1f}, "
              f"標準差={results['sum_statistics']['std']:.1f}")
        
        return results
    
    def correlation_analysis(self):
        """相關性分析"""
        print("\n=== 相關性分析 ===")
        
        # 計算號碼間的相關係數
        correlation_matrix = self.numbers_data.corr()
        
        # 計算平均相關係數
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool), k=1)
        upper_triangle = correlation_matrix.where(mask)
        mean_correlation = upper_triangle.stack().mean()
        
        # 找出最強相關的號碼對
        corr_pairs = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                corr_value = correlation_matrix.iloc[i, j]
                if abs(corr_value) > 0.1:  # 閾值可調整
                    corr_pairs.append((i+1, j+1, corr_value))
        
        corr_pairs.sort(key=lambda x: abs(x[2]), reverse=True)
        
        results = {
            'correlation_matrix': correlation_matrix,
            'mean_correlation': mean_correlation,
            'strong_correlations': corr_pairs[:10]  # 前10個最強相關
        }
        
        self.analysis_results['correlation'] = results
        
        print(f"平均相關係數: {mean_correlation:.4f}")
        print("最強相關號碼對:")
        for pair in corr_pairs[:5]:
            print(f"  號碼{pair[0]} - 號碼{pair[1]}: {pair[2]:.4f}")
        
        return results
    
    def pattern_analysis(self):
        """模式分析"""
        print("\n=== 模式分析 ===")
        
        results = {}
        
        # 1. 連號分析
        consecutive_counts = []
        for _, row in self.numbers_data.iterrows():
            numbers = sorted(row.values)
            consecutive = 0
            max_consecutive = 0
            
            for i in range(1, len(numbers)):
                if numbers[i] == numbers[i-1] + 1:
                    consecutive += 1
                    max_consecutive = max(max_consecutive, consecutive + 1)
                else:
                    consecutive = 0
            
            consecutive_counts.append(max_consecutive)
        
        consecutive_freq = pd.Series(consecutive_counts).value_counts().sort_index()
        results['consecutive_analysis'] = consecutive_freq
        
        # 2. AC值分析 (算術複雜度)
        ac_values = []
        for _, row in self.numbers_data.iterrows():
            numbers = sorted(row.values)
            differences = []
            for i in range(len(numbers)):
                for j in range(i+1, len(numbers)):
                    differences.append(abs(numbers[i] - numbers[j]))
            
            unique_diffs = len(set(differences))
            ac_value = unique_diffs - 5  # AC值計算公式
            ac_values.append(ac_value)
        
        results['ac_statistics'] = {
            'mean': np.mean(ac_values),
            'std': np.std(ac_values),
            'distribution': pd.Series(ac_values).value_counts().sort_index()
        }
        
        # 3. 重複號分析（與上期重複的號碼數量）
        repeat_counts = []
        for i in range(1, len(self.numbers_data)):
            current_numbers = set(self.numbers_data.iloc[i].values)
            previous_numbers = set(self.numbers_data.iloc[i-1].values)
            repeat_count = len(current_numbers.intersection(previous_numbers))
            repeat_counts.append(repeat_count)
        
        repeat_freq = pd.Series(repeat_counts).value_counts().sort_index()
        results['repeat_analysis'] = repeat_freq
        
        self.analysis_results['pattern'] = results
        
        print(f"連號分佈: {dict(consecutive_freq)}")
        print(f"AC值統計: 平均={results['ac_statistics']['mean']:.2f}, "
              f"標準差={results['ac_statistics']['std']:.2f}")
        print(f"重複號分佈: {dict(repeat_freq)}")
        
        return results
    
    def statistical_tests(self):
        """統計檢驗"""
        print("\n=== 統計檢驗 ===")
        
        results = {}
        
        # 1. 均勻性檢驗 (卡方檢驗)
        all_numbers = self.numbers_data.values.flatten()
        observed_freq = pd.Series(all_numbers).value_counts()
        
        # 確保所有號碼都有記錄
        for i in range(1, 50):
            if i not in observed_freq.index:
                observed_freq[i] = 0
        
        observed_freq = observed_freq.sort_index()
        expected_freq = len(all_numbers) / 49
        
        chi2_stat, chi2_p = stats.chisquare(observed_freq.values)
        results['uniformity_test'] = {
            'chi2_statistic': chi2_stat,
            'p_value': chi2_p,
            'is_uniform': chi2_p > 0.05
        }
        
        # 2. 正態性檢驗 (和值)
        sum_values = self.numbers_data.sum(axis=1)
        shapiro_stat, shapiro_p = stats.shapiro(sum_values)
        results['normality_test'] = {
            'shapiro_statistic': shapiro_stat,
            'p_value': shapiro_p,
            'is_normal': shapiro_p > 0.05
        }
        
        # 3. 獨立性檢驗
        # 檢驗相鄰期數的獨立性
        if len(self.numbers_data) > 1:
            current_sums = self.numbers_data.sum(axis=1)[1:].values
            previous_sums = self.numbers_data.sum(axis=1)[:-1].values
            
            correlation, corr_p = stats.pearsonr(current_sums, previous_sums)
            results['independence_test'] = {
                'correlation': correlation,
                'p_value': corr_p,
                'is_independent': corr_p > 0.05
            }
        
        self.analysis_results['statistical_tests'] = results
        
        print(f"均勻性檢驗: χ²={chi2_stat:.2f}, p={chi2_p:.4f}, "
              f"均勻分佈: {'是' if chi2_p > 0.05 else '否'}")
        print(f"正態性檢驗: W={shapiro_stat:.4f}, p={shapiro_p:.4f}, "
              f"正態分佈: {'是' if shapiro_p > 0.05 else '否'}")
        
        if 'independence_test' in results:
            print(f"獨立性檢驗: r={correlation:.4f}, p={corr_p:.4f}, "
                  f"獨立: {'是' if corr_p > 0.05 else '否'}")
        
        return results
    
    def comprehensive_analysis(self):
        """綜合分析"""
        print("開始大樂透綜合統計分析...")
        print("=" * 50)
        
        # 執行所有分析
        freq_results = self.frequency_analysis()
        dist_results = self.distribution_analysis()
        corr_results = self.correlation_analysis()
        pattern_results = self.pattern_analysis()
        test_results = self.statistical_tests()
        
        # 生成綜合報告
        self._generate_report()
        
        return self.analysis_results
    
    def _generate_report(self):
        """生成分析報告"""
        print("\n" + "=" * 50)
        print("綜合分析報告")
        print("=" * 50)
        
        # 基本統計摘要
        print("\n【基本統計摘要】")
        print(f"分析期數: {len(self.data)} 期")
        print(f"數據完整性: {'完整' if self.numbers_data.isnull().sum().sum() == 0 else '有缺失'}")
        
        # 頻率分析摘要
        if 'frequency' in self.analysis_results:
            freq_data = self.analysis_results['frequency']
            print(f"\n【頻率分析摘要】")
            print(f"變異係數: {freq_data['cv_frequency']:.3f}")
            print(f"熱門號碼數量: {len(freq_data['hot_numbers'])}")
            print(f"冷門號碼數量: {len(freq_data['cold_numbers'])}")
        
        # 分佈特徵摘要
        if 'distribution' in self.analysis_results:
            dist_data = self.analysis_results['distribution']
            print(f"\n【分佈特徵摘要】")
            print(f"平均和值: {dist_data['sum_statistics']['mean']:.1f}")
            print(f"和值標準差: {dist_data['sum_statistics']['std']:.1f}")
        
        # 統計檢驗摘要
        if 'statistical_tests' in self.analysis_results:
            test_data = self.analysis_results['statistical_tests']
            print(f"\n【統計檢驗摘要】")
            print(f"號碼分佈均勻性: {'通過' if test_data['uniformity_test']['is_uniform'] else '不通過'}")
            print(f"和值正態性: {'通過' if test_data['normality_test']['is_normal'] else '不通過'}")
        
        print("\n分析完成！")
        print("注意: 以上分析僅供統計研究參考，不保證預測準確性。")

def create_sample_data(periods=100):
    """創建示例數據用於測試"""
    np.random.seed(42)
    data = []
    
    for i in range(periods):
        # 隨機生成6個不重複的號碼
        numbers = np.random.choice(range(1, 50), size=6, replace=False)
        numbers = sorted(numbers)
        
        period_data = {
            'period': f'第{i+1:03d}期',
            'number1': numbers[0],
            'number2': numbers[1],
            'number3': numbers[2],
            'number4': numbers[3],
            'number5': numbers[4],
            'number6': numbers[5],
            'special': np.random.choice([n for n in range(1, 50) if n not in numbers])
        }
        data.append(period_data)
    
    return pd.DataFrame(data)

if __name__ == "__main__":
    # 示例使用
    print("大樂透統計分析工具")
    print("=" * 30)
    
    # 創建示例數據
    sample_data = create_sample_data(200)
    
    # 初始化分析器
    analyzer = LotteryAnalyzer()
    analyzer.load_data(sample_data)
    
    # 執行綜合分析
    results = analyzer.comprehensive_analysis()
    
    print("\n分析結果已保存在 analyzer.analysis_results 中")
    print("可以使用以下方法查看具體結果:")
    print("- analyzer.analysis_results['frequency']")
    print("- analyzer.analysis_results['distribution']")
    print("- analyzer.analysis_results['correlation']")
    print("- analyzer.analysis_results['pattern']")
    print("- analyzer.analysis_results['statistical_tests']")
