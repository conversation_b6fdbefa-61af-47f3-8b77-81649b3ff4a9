version: '3.8'

services:
  # 大樂透分析核心服務
  lottery-analyzer:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: lottery-analyzer
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./output:/app/output
      - ./config:/app/config
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
      - MPLBACKEND=Agg
      - TZ=Asia/Taipei
    restart: unless-stopped
    networks:
      - lottery-network
    healthcheck:
      test: ["CMD", "python", "-c", "import 大樂透統計分析工具; print('OK')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Web界面服務 (可選)
  lottery-web:
    build:
      context: .
      dockerfile: Dockerfile.web
    container_name: lottery-web
    ports:
      - "8000:8000"
    depends_on:
      - lottery-analyzer
      - redis
    environment:
      - FLASK_ENV=production
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=postgresql://lottery_user:${POSTGRES_PASSWORD}@postgres:5432/lottery
    volumes:
      - ./static:/app/static
      - ./templates:/app/templates
    restart: unless-stopped
    networks:
      - lottery-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 數據庫服務
  postgres:
    image: postgres:13-alpine
    container_name: lottery-db
    environment:
      POSTGRES_DB: lottery
      POSTGRES_USER: lottery_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_123}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - lottery-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U lottery_user -d lottery"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis緩存服務
  redis:
    image: redis:6-alpine
    container_name: lottery-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password_123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - lottery-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Jupyter Notebook服務 (開發環境)
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile.jupyter
    container_name: lottery-jupyter
    ports:
      - "8888:8888"
    volumes:
      - ./notebooks:/app/notebooks
      - ./data:/app/data
      - .:/app/src
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=${JUPYTER_TOKEN:-lottery_jupyter_token}
    restart: unless-stopped
    networks:
      - lottery-network
    profiles:
      - development

  # 監控服務 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: lottery-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - lottery-network
    profiles:
      - monitoring

  # 監控服務 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: lottery-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin_password_123}
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: unless-stopped
    networks:
      - lottery-network
    profiles:
      - monitoring

  # 日誌收集服務
  filebeat:
    image: docker.elastic.co/beats/filebeat:7.15.0
    container_name: lottery-filebeat
    user: root
    volumes:
      - ./logs:/app/logs:ro
      - ./monitoring/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - ELASTICSEARCH_HOST=${ELASTICSEARCH_HOST:-elasticsearch:9200}
    restart: unless-stopped
    networks:
      - lottery-network
    profiles:
      - logging

  # 反向代理
  nginx:
    image: nginx:alpine
    container_name: lottery-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./static:/var/www/static:ro
    depends_on:
      - lottery-web
    restart: unless-stopped
    networks:
      - lottery-network
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  lottery-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
