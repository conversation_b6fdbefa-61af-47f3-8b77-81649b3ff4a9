# 樂透統計方法快速參考

## 📊 核心統計概念

### 基本機率
```python
# 大樂透基本機率計算
total_combinations = math.comb(49, 6)  # 13,983,816
win_probability = 1 / total_combinations  # 約 0.0000071%

# 單個號碼被選中的機率
single_number_prob = 6 / 49  # 約 12.24%
```

### 期望值計算
```python
# 每個號碼的期望出現次數
expected_frequency = total_periods * 6 / 49

# 和值的期望值
expected_sum = 6 * (1 + 49) / 2  # = 150
```

## 🔍 描述性統計

### 中心趨勢
```python
import numpy as np

# 計算號碼頻率的中心趨勢
frequencies = [count_number(i) for i in range(1, 50)]

mean_freq = np.mean(frequencies)
median_freq = np.median(frequencies)
mode_freq = max(set(frequencies), key=frequencies.count)
```

### 離散程度
```python
# 變異係數 - 衡量分佈均勻性
cv = np.std(frequencies) / np.mean(frequencies)
# CV接近0表示分佈均勻

# 四分位距
q1 = np.percentile(frequencies, 25)
q3 = np.percentile(frequencies, 75)
iqr = q3 - q1
```

### 分佈形狀
```python
from scipy import stats

# 偏度 - 衡量對稱性
skewness = stats.skew(frequencies)
# 接近0表示對稱分佈

# 峰度 - 衡量尖銳程度
kurtosis = stats.kurtosis(frequencies)
# 接近0表示正態分佈
```

## 📈 機率分佈檢驗

### 均勻分佈檢驗
```python
from scipy.stats import chisquare

# 卡方檢驗號碼頻率是否均勻分佈
observed_freq = [count_number(i) for i in range(1, 50)]
expected_freq = [total_periods * 6 / 49] * 49

chi2_stat, p_value = chisquare(observed_freq, expected_freq)

if p_value > 0.05:
    print("符合均勻分佈")
else:
    print("不符合均勻分佈")
```

### 正態分佈檢驗
```python
from scipy.stats import shapiro, normaltest

# Shapiro-Wilk檢驗
stat, p_value = shapiro(sum_values)

# D'Agostino檢驗
stat, p_value = normaltest(sum_values)

if p_value > 0.05:
    print("符合正態分佈")
```

### 獨立性檢驗
```python
from scipy.stats import pearsonr

# 檢驗相鄰期數的相關性
current_sums = sum_values[1:]
previous_sums = sum_values[:-1]

correlation, p_value = pearsonr(current_sums, previous_sums)

if abs(correlation) < 0.1 and p_value > 0.05:
    print("期間獨立")
```

## 🎯 頻率分析

### 熱冷號碼分類
```python
def classify_numbers(frequencies):
    mean_freq = np.mean(frequencies)
    std_freq = np.std(frequencies)
    
    hot_threshold = mean_freq + std_freq
    cold_threshold = mean_freq - std_freq
    
    hot_numbers = [i for i, freq in enumerate(frequencies, 1) 
                   if freq > hot_threshold]
    cold_numbers = [i for i, freq in enumerate(frequencies, 1) 
                    if freq < cold_threshold]
    
    return hot_numbers, cold_numbers
```

### 頻率趨勢分析
```python
def frequency_trend(number, periods, window=20):
    """計算號碼頻率趨勢"""
    appearances = [1 if number in period else 0 for period in periods]
    
    # 移動平均
    moving_avg = []
    for i in range(window, len(appearances)):
        avg = sum(appearances[i-window:i]) / window
        moving_avg.append(avg)
    
    return moving_avg
```

## ⏱️ 間隔分析

### 間隔統計
```python
def calculate_intervals(number, lottery_data):
    """計算號碼出現間隔"""
    appearances = []
    for idx, numbers in enumerate(lottery_data):
        if number in numbers:
            appearances.append(idx)
    
    if len(appearances) < 2:
        return []
    
    intervals = [appearances[i+1] - appearances[i] 
                for i in range(len(appearances)-1)]
    
    return {
        'intervals': intervals,
        'mean_interval': np.mean(intervals),
        'std_interval': np.std(intervals),
        'current_interval': len(lottery_data) - appearances[-1] - 1
    }
```

### 間隔預測
```python
def predict_appearance(interval_data):
    """基於間隔統計預測出現時機"""
    mean_interval = interval_data['mean_interval']
    std_interval = interval_data['std_interval']
    current_interval = interval_data['current_interval']
    
    if current_interval > mean_interval + std_interval:
        return "可能即將出現"
    elif current_interval < mean_interval - std_interval:
        return "剛剛出現，短期內不太可能"
    else:
        expected_periods = int(mean_interval - current_interval)
        return f"預計{expected_periods}期後出現"
```

## 🎨 模式分析

### 連號檢測
```python
def count_consecutive(numbers):
    """計算連續號碼數量"""
    sorted_numbers = sorted(numbers)
    consecutive = 0
    max_consecutive = 0
    
    for i in range(1, len(sorted_numbers)):
        if sorted_numbers[i] == sorted_numbers[i-1] + 1:
            consecutive += 1
            max_consecutive = max(max_consecutive, consecutive + 1)
        else:
            consecutive = 0
    
    return max_consecutive
```

### 奇偶模式
```python
def analyze_odd_even_pattern(lottery_data):
    """分析奇偶模式"""
    patterns = []
    for numbers in lottery_data:
        odd_count = sum(1 for n in numbers if n % 2 == 1)
        patterns.append(odd_count)
    
    pattern_freq = {i: patterns.count(i) for i in range(7)}
    return pattern_freq
```

### 和值分析
```python
def sum_value_analysis(lottery_data):
    """和值統計分析"""
    sums = [sum(numbers) for numbers in lottery_data]
    
    return {
        'mean': np.mean(sums),
        'std': np.std(sums),
        'min': min(sums),
        'max': max(sums),
        'most_common_range': find_most_common_range(sums)
    }

def find_most_common_range(sums, range_size=20):
    """找出最常見的和值範圍"""
    ranges = {}
    for s in sums:
        range_key = (s // range_size) * range_size
        ranges[range_key] = ranges.get(range_key, 0) + 1
    
    most_common = max(ranges, key=ranges.get)
    return (most_common, most_common + range_size - 1)
```

## 📊 高級統計方法

### 自相關分析
```python
from statsmodels.tsa.stattools import acf

def autocorrelation_analysis(number, lottery_data, lags=20):
    """計算號碼出現的自相關"""
    series = [1 if number in period else 0 for period in lottery_data]
    autocorr = acf(series, nlags=lags)
    return autocorr
```

### 馬可夫鏈分析
```python
def markov_transition_matrix(sequence, states):
    """建立馬可夫轉移矩陣"""
    n_states = len(states)
    matrix = np.zeros((n_states, n_states))
    
    for i in range(len(sequence) - 1):
        current = states.index(sequence[i])
        next_state = states.index(sequence[i + 1])
        matrix[current][next_state] += 1
    
    # 正規化
    for i in range(n_states):
        row_sum = matrix[i].sum()
        if row_sum > 0:
            matrix[i] /= row_sum
    
    return matrix
```

### 遊程檢驗
```python
def runs_test(sequence):
    """遊程檢驗 - 檢驗隨機性"""
    runs = 1
    for i in range(1, len(sequence)):
        if sequence[i] != sequence[i-1]:
            runs += 1
    
    n1 = sequence.count(0)
    n2 = sequence.count(1)
    
    expected_runs = (2 * n1 * n2) / (n1 + n2) + 1
    variance = (2 * n1 * n2 * (2 * n1 * n2 - n1 - n2)) / \
               ((n1 + n2) ** 2 * (n1 + n2 - 1))
    
    z_score = (runs - expected_runs) / np.sqrt(variance)
    
    # 雙尾檢驗
    p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
    
    return z_score, p_value
```

## 📋 實用工具函數

### 數據驗證
```python
def validate_lottery_data(data):
    """驗證樂透數據有效性"""
    errors = []
    
    for idx, numbers in enumerate(data):
        # 檢查號碼數量
        if len(numbers) != 6:
            errors.append(f"第{idx+1}期號碼數量錯誤")
        
        # 檢查號碼範圍
        if any(n < 1 or n > 49 for n in numbers):
            errors.append(f"第{idx+1}期號碼超出範圍")
        
        # 檢查重複號碼
        if len(set(numbers)) != len(numbers):
            errors.append(f"第{idx+1}期有重複號碼")
    
    return errors
```

### 統計摘要
```python
def statistical_summary(lottery_data):
    """生成統計摘要報告"""
    total_periods = len(lottery_data)
    all_numbers = [n for period in lottery_data for n in period]
    
    summary = {
        'total_periods': total_periods,
        'total_numbers': len(all_numbers),
        'unique_numbers': len(set(all_numbers)),
        'most_frequent': max(set(all_numbers), key=all_numbers.count),
        'least_frequent': min(set(all_numbers), key=all_numbers.count),
        'frequency_cv': np.std([all_numbers.count(i) for i in range(1, 50)]) / 
                       np.mean([all_numbers.count(i) for i in range(1, 50)])
    }
    
    return summary
```

### 可視化輔助
```python
import matplotlib.pyplot as plt

def plot_frequency_distribution(frequencies):
    """繪製頻率分佈圖"""
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    plt.bar(range(1, 50), frequencies)
    plt.title('號碼出現頻率')
    plt.xlabel('號碼')
    plt.ylabel('出現次數')
    
    plt.subplot(1, 2, 2)
    plt.hist(frequencies, bins=10, alpha=0.7)
    plt.title('頻率分佈直方圖')
    plt.xlabel('出現次數')
    plt.ylabel('號碼數量')
    
    plt.tight_layout()
    plt.show()

def plot_sum_distribution(sum_values):
    """繪製和值分佈圖"""
    plt.figure(figsize=(10, 6))
    
    plt.hist(sum_values, bins=30, alpha=0.7, density=True)
    plt.title('和值分佈')
    plt.xlabel('和值')
    plt.ylabel('密度')
    
    # 添加正態分佈曲線
    mu, sigma = np.mean(sum_values), np.std(sum_values)
    x = np.linspace(min(sum_values), max(sum_values), 100)
    y = stats.norm.pdf(x, mu, sigma)
    plt.plot(x, y, 'r-', label=f'正態分佈 (μ={mu:.1f}, σ={sigma:.1f})')
    
    plt.legend()
    plt.show()
```

## ⚠️ 重要提醒

### 統計分析的局限性
1. **隨機性本質**：樂透本質上是隨機事件
2. **獨立性原理**：每期開獎相互獨立
3. **機率不變**：統計分析不能改變中獎機率
4. **樣本偏差**：有限樣本可能存在偏差

### 正確使用統計結果
- 用於學術研究和統計學習
- 增加選號的娛樂性
- 驗證隨機性理論
- **不要**作為投資依據

### 風險控制
- 設定合理預算
- 理性參與
- 量力而為
- 娛樂心態

---

*本參考指南僅供統計學習使用，請理性參與樂透活動。*
