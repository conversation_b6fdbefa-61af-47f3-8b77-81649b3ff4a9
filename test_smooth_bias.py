#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試平滑BIAS指標 - 不包含圖表顯示
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class SimpleSmoothBias:
    """簡化版平滑BIAS指標，用於測試"""
    
    def __init__(self):
        self.data = None
        self.bias_period = 12
        self.smooth_periods = [20, 60]
    
    def generate_sample_data(self, days=120):
        """生成模擬數據"""
        start_date = datetime.now() - timedelta(days=days)
        dates = [start_date + timedelta(days=i) for i in range(days)]
        
        np.random.seed(42)
        base_price = 100
        trend = np.sin(np.linspace(0, 4*np.pi, days)) * 5
        long_trend = np.linspace(0, 15, days)
        noise = np.random.normal(0, 1.5, days)
        prices = base_price + trend + long_trend + noise
        prices = np.maximum(prices, 50)
        
        self.data = pd.DataFrame({
            'date': dates,
            'close': prices
        })
        return self.data
    
    def calculate_indicators(self):
        """計算所有指標"""
        # 計算移動平均線
        self.data[f'MA{self.bias_period}'] = self.data['close'].rolling(window=self.bias_period).mean()
        
        # 計算BIAS
        self.data[f'BIAS{self.bias_period}'] = ((self.data['close'] - self.data[f'MA{self.bias_period}']) /
                                               self.data[f'MA{self.bias_period}']) * 100
        
        # 計算平滑BIAS
        for period in self.smooth_periods:
            self.data[f'SmoothBIAS{period}'] = self.data[f'BIAS{self.bias_period}'].rolling(window=period).mean()
        
        # 計算差值
        self.data['BIAS_Diff'] = (self.data[f'SmoothBIAS{self.smooth_periods[0]}'] - 
                                 self.data[f'SmoothBIAS{self.smooth_periods[1]}'])
        
        return self.data
    
    def get_latest_values(self):
        """獲取最新值"""
        if self.data is None:
            return None
        
        latest = self.data.iloc[-1]
        return {
            'date': latest['date'],
            'price': latest['close'],
            'ma': latest[f'MA{self.bias_period}'],
            'bias': latest[f'BIAS{self.bias_period}'],
            'smooth_bias_20': latest[f'SmoothBIAS{self.smooth_periods[0]}'],
            'smooth_bias_60': latest[f'SmoothBIAS{self.smooth_periods[1]}'],
            'bias_diff': latest['BIAS_Diff']
        }
    
    def print_analysis(self):
        """打印分析結果"""
        values = self.get_latest_values()
        if values is None:
            print("無數據可分析")
            return
        
        print("=" * 50)
        print("平滑BIAS指標分析")
        print("=" * 50)
        print(f"日期: {values['date'].strftime('%Y-%m-%d')}")
        print(f"股價: {values['price']:.2f}")
        print(f"{self.bias_period}日MA: {values['ma']:.2f}")
        print(f"原始BIAS: {values['bias']:.2f}%")
        print(f"20日平滑BIAS: {values['smooth_bias_20']:.2f}%")
        print(f"60日平滑BIAS: {values['smooth_bias_60']:.2f}%")
        print(f"BIAS差值: {values['bias_diff']:.2f}%")
        
        # 趨勢判斷
        if values['bias_diff'] > 1:
            trend = "多頭趨勢"
        elif values['bias_diff'] < -1:
            trend = "空頭趨勢"
        else:
            trend = "震盪整理"
        
        print(f"趨勢狀態: {trend}")
        print("=" * 50)

def main():
    """主測試函數"""
    print("🧪 平滑BIAS指標測試")
    print("-" * 30)
    
    # 創建指標實例
    indicator = SimpleSmoothBias()
    
    # 生成數據
    print("生成測試數據...")
    indicator.generate_sample_data(120)
    
    # 計算指標
    print("計算指標...")
    indicator.calculate_indicators()
    
    # 顯示結果
    indicator.print_analysis()
    
    # 顯示數據統計
    data = indicator.data
    print(f"\n數據統計:")
    print(f"總期數: {len(data)}")
    print(f"價格範圍: {data['close'].min():.2f} - {data['close'].max():.2f}")
    print(f"BIAS範圍: {data[f'BIAS{indicator.bias_period}'].min():.2f}% - {data[f'BIAS{indicator.bias_period}'].max():.2f}%")
    
    # 檢查平滑效果
    bias_std = data[f'BIAS{indicator.bias_period}'].std()
    smooth_bias_20_std = data['SmoothBIAS20'].std()
    smooth_bias_60_std = data['SmoothBIAS60'].std()
    
    print(f"\n平滑效果:")
    print(f"原始BIAS標準差: {bias_std:.3f}")
    print(f"20日平滑BIAS標準差: {smooth_bias_20_std:.3f}")
    print(f"60日平滑BIAS標準差: {smooth_bias_60_std:.3f}")
    print(f"平滑效果: {((bias_std - smooth_bias_20_std) / bias_std * 100):.1f}% (20日)")
    
    print("\n✅ 測試完成！")

if __name__ == "__main__":
    main()
