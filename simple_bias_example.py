import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 简单的BIAS指标计算示例

def calculate_bias(prices, period=20):
    """
    计算BIAS指标
    
    参数:
    prices: 价格序列（列表或数组）
    period: 移动平均线周期
    
    返回:
    bias_values: BIAS值列表
    ma_values: 移动平均线值列表
    """
    prices = np.array(prices)
    ma_values = []
    bias_values = []
    
    for i in range(len(prices)):
        if i >= period - 1:
            # 计算移动平均线
            ma = np.mean(prices[i-period+1:i+1])
            ma_values.append(ma)
            
            # 计算BIAS值
            bias = (prices[i] - ma) / ma * 100
            bias_values.append(bias)
        else:
            ma_values.append(None)
            bias_values.append(None)
    
    return bias_values, ma_values

def plot_simple_bias(prices, period=20):
    """绘制简单的BIAS图表"""
    bias_values, ma_values = calculate_bias(prices, period)
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
    
    # 上图：价格和移动平均线
    days = range(len(prices))
    ax1.plot(days, prices, 'b-', label='股价', linewidth=2)
    ax1.plot(days, ma_values, 'r-', label=f'{period}日均线', linewidth=2)
    ax1.set_title('股价与移动平均线')
    ax1.set_ylabel('价格')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 下图：BIAS指标
    ax2.plot(days, bias_values, 'g-', label=f'BIAS{period}', linewidth=2)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax2.axhline(y=10, color='red', linestyle='--', alpha=0.7, label='超买线')
    ax2.axhline(y=-10, color='blue', linestyle='--', alpha=0.7, label='超卖线')
    ax2.set_title('BIAS指标')
    ax2.set_xlabel('天数')
    ax2.set_ylabel('BIAS值 (%)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# 示例数据
if __name__ == "__main__":
    # 模拟股价数据
    np.random.seed(42)
    base_price = 100
    days = 50
    
    # 生成带趋势的价格数据
    trend = np.linspace(0, 5, days)
    noise = np.random.normal(0, 1.5, days)
    prices = base_price + trend + noise
    
    print("BIAS移动平均线指标计算示例")
    print("=" * 40)
    
    # 计算BIAS
    bias_values, ma_values = calculate_bias(prices, period=10)
    
    # 显示最后几天的数据
    print("最近10天的数据:")
    print("日期\t股价\t10日均线\tBIAS值")
    print("-" * 40)
    
    for i in range(-10, 0):
        day = len(prices) + i + 1
        price = prices[i]
        ma = ma_values[i] if ma_values[i] is not None else 0
        bias = bias_values[i] if bias_values[i] is not None else 0
        
        print(f"第{day}天\t{price:.2f}\t{ma:.2f}\t\t{bias:.2f}%")
    
    # 分析当前状态
    current_bias = bias_values[-1]
    print(f"\n当前BIAS值: {current_bias:.2f}%")
    
    if current_bias > 10:
        print("状态: 超买，可能回调")
    elif current_bias < -10:
        print("状态: 超卖，可能反弹")
    else:
        print("状态: 正常区间")
    
    # 绘制图表
    plot_simple_bias(prices, period=10)
    
    # 手动计算示例
    print("\n手动计算示例:")
    print("假设当前股价: 105")
    print("10日移动平均线: 100")
    print("BIAS = (105 - 100) / 100 × 100% = 5%")
    
    manual_bias = (105 - 100) / 100 * 100
    print(f"计算结果: {manual_bias}%")
