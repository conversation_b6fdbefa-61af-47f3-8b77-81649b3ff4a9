# 大樂透統計方法大全

## 完整統計方法清單

### 一、基礎統計方法

#### 1.1 描述性統計
- **中心趨勢測量**
  - 算術平均數 (Arithmetic Mean)
  - 幾何平均數 (Geometric Mean)
  - 調和平均數 (Harmonic Mean)
  - 中位數 (Median)
  - 眾數 (Mode)
  - 截尾平均數 (Trimmed Mean)

- **變異性測量**
  - 全距 (Range)
  - 四分位距 (Interquartile Range, IQR)
  - 變異數 (Variance)
  - 標準差 (Standard Deviation)
  - 變異係數 (Coefficient of Variation)
  - 平均絕對偏差 (Mean Absolute Deviation)

- **分佈形狀測量**
  - 偏度 (Skewness)
  - 峰度 (Kurtosis)
  - 分位數 (Quantiles)
  - 百分位數 (Percentiles)

#### 1.2 頻率分析
- **出現頻率統計**
  - 絕對頻率 (Absolute Frequency)
  - 相對頻率 (Relative Frequency)
  - 累積頻率 (Cumulative Frequency)
  - 頻率密度 (Frequency Density)

- **頻率分佈檢驗**
  - 卡方適合度檢驗 (Chi-square Goodness of Fit)
  - 柯爾莫哥洛夫-斯米爾諾夫檢驗 (K-S Test)
  - 安德森-達令檢驗 (Anderson-Darling Test)
  - 夏皮羅-威爾克檢驗 (Shapiro-Wilk Test)

### 二、機率與分佈分析

#### 2.1 機率分佈
- **離散分佈**
  - 二項分佈 (Binomial Distribution)
  - 泊松分佈 (Poisson Distribution)
  - 幾何分佈 (Geometric Distribution)
  - 負二項分佈 (Negative Binomial Distribution)
  - 超幾何分佈 (Hypergeometric Distribution)

- **連續分佈**
  - 正態分佈 (Normal Distribution)
  - 均勻分佈 (Uniform Distribution)
  - 指數分佈 (Exponential Distribution)
  - 伽瑪分佈 (Gamma Distribution)
  - 貝塔分佈 (Beta Distribution)

#### 2.2 機率模型
- **條件機率模型**
  - 貝葉斯定理應用
  - 全機率公式
  - 條件獨立性檢驗

- **隨機過程模型**
  - 馬可夫鏈 (Markov Chain)
  - 隱馬可夫模型 (Hidden Markov Model)
  - 隨機遊走 (Random Walk)

### 三、假設檢驗方法

#### 3.1 參數檢驗
- **單樣本檢驗**
  - 單樣本t檢驗
  - 單樣本z檢驗
  - 單樣本比例檢驗

- **雙樣本檢驗**
  - 獨立樣本t檢驗
  - 配對樣本t檢驗
  - 雙樣本比例檢驗
  - F檢驗 (變異數齊性檢驗)

#### 3.2 無母數檢驗
- **單樣本檢驗**
  - 符號檢驗 (Sign Test)
  - 威爾科克森符號秩檢驗 (Wilcoxon Signed-rank Test)

- **雙樣本檢驗**
  - 曼-惠特尼U檢驗 (Mann-Whitney U Test)
  - 威爾科克森秩和檢驗 (Wilcoxon Rank-sum Test)
  - 柯爾莫哥洛夫-斯米爾諾夫雙樣本檢驗

- **多樣本檢驗**
  - 克魯斯卡爾-沃利斯檢驗 (Kruskal-Wallis Test)
  - 弗里德曼檢驗 (Friedman Test)

### 四、相關與回歸分析

#### 4.1 相關分析
- **線性相關**
  - 皮爾森相關係數 (Pearson Correlation)
  - 偏相關係數 (Partial Correlation)
  - 複相關係數 (Multiple Correlation)

- **非線性相關**
  - 斯皮爾曼等級相關 (Spearman Rank Correlation)
  - 肯德爾τ相關 (Kendall's Tau)
  - 距離相關 (Distance Correlation)

#### 4.2 回歸分析
- **線性回歸**
  - 簡單線性回歸
  - 多元線性回歸
  - 逐步回歸 (Stepwise Regression)
  - 嶺回歸 (Ridge Regression)
  - 套索回歸 (Lasso Regression)

- **非線性回歸**
  - 多項式回歸
  - 邏輯回歸 (Logistic Regression)
  - 泊松回歸 (Poisson Regression)
  - 非線性最小平方法

### 五、時間序列分析

#### 5.1 趨勢分析
- **移動平均法**
  - 簡單移動平均 (Simple Moving Average)
  - 加權移動平均 (Weighted Moving Average)
  - 指數移動平均 (Exponential Moving Average)

- **趨勢擬合**
  - 線性趨勢
  - 多項式趨勢
  - 指數趨勢
  - 對數趨勢

#### 5.2 季節性分析
- **分解方法**
  - 加法分解模型
  - 乘法分解模型
  - STL分解 (Seasonal and Trend decomposition using Loess)

- **季節性檢驗**
  - 季節性單位根檢驗
  - HEGY檢驗
  - 季節性協整檢驗

#### 5.3 平穩性檢驗
- **單位根檢驗**
  - ADF檢驗 (Augmented Dickey-Fuller)
  - PP檢驗 (Phillips-Perron)
  - KPSS檢驗

### 六、多變量統計分析

#### 6.1 降維技術
- **主成分分析 (PCA)**
  - 標準化PCA
  - 穩健PCA
  - 核PCA (Kernel PCA)

- **因子分析**
  - 探索性因子分析 (EFA)
  - 驗證性因子分析 (CFA)
  - 最大似然因子分析

#### 6.2 聚類分析
- **階層聚類**
  - 單連結法 (Single Linkage)
  - 完全連結法 (Complete Linkage)
  - 平均連結法 (Average Linkage)
  - 華德法 (Ward's Method)

- **分割聚類**
  - K-means聚類
  - K-medoids聚類
  - 模糊C-means聚類

#### 6.3 判別分析
- **線性判別分析 (LDA)**
- **二次判別分析 (QDA)**
- **正則化判別分析**

### 七、進階統計方法

#### 7.1 貝葉斯統計
- **貝葉斯推論**
  - 先驗分佈選擇
  - 後驗分佈計算
  - 貝葉斯因子

- **馬可夫鏈蒙地卡羅 (MCMC)**
  - Gibbs抽樣
  - Metropolis-Hastings算法
  - 漢密爾頓蒙地卡羅 (HMC)

#### 7.2 重抽樣方法
- **自助法 (Bootstrap)**
  - 非參數自助法
  - 參數自助法
  - 偏差校正自助法

- **交叉驗證**
  - k-fold交叉驗證
  - 留一法交叉驗證 (LOOCV)
  - 分層交叉驗證

#### 7.3 穩健統計
- **穩健估計**
  - M-估計量
  - L-估計量
  - R-估計量

- **異常值檢測**
  - 箱型圖法
  - Z-score法
  - 修正Z-score法
  - 孤立森林 (Isolation Forest)

### 八、專門分析技術

#### 8.1 組合分析
- **AC值分析**
  - 算術複雜度
  - 幾何複雜度
  - 組合複雜度

- **和值分析**
  - 和值分佈
  - 和值趨勢
  - 和值週期

#### 8.2 間隔分析
- **遺漏值分析**
  - 最大遺漏
  - 平均遺漏
  - 當前遺漏

- **冷熱分析**
  - 熱號識別
  - 冷號識別
  - 溫號識別

#### 8.3 模式識別
- **重複號分析**
- **連號分析**
- **同尾號分析**
- **奇偶比分析**
- **大小比分析**

### 九、模型評估與驗證

#### 9.1 模型選擇
- **信息準則**
  - AIC (Akaike Information Criterion)
  - BIC (Bayesian Information Criterion)
  - AICc (Corrected AIC)

- **交叉驗證評分**
  - 均方誤差 (MSE)
  - 平均絕對誤差 (MAE)
  - R平方值

#### 9.2 預測評估
- **準確性測量**
  - 命中率
  - 精確率
  - 召回率
  - F1分數

- **穩定性測量**
  - 預測區間
  - 信賴區間
  - 預測標準誤

### 十、實用工具與技術

#### 10.1 數據視覺化
- **基本圖表**
  - 直方圖
  - 散點圖
  - 箱型圖
  - 小提琴圖

- **進階圖表**
  - 熱力圖
  - 相關矩陣圖
  - 時間序列圖
  - 3D散點圖

#### 10.2 統計軟體應用
- **R語言套件**
  - base R統計函數
  - ggplot2視覺化
  - dplyr數據處理
  - forecast時間序列

- **Python套件**
  - NumPy數值計算
  - Pandas數據分析
  - SciPy統計函數
  - Matplotlib視覺化
  - Seaborn統計圖表
  - Scikit-learn機器學習

---

## 使用建議

1. **循序漸進**：從基礎統計開始，逐步應用進階方法
2. **多方法驗證**：使用多種統計方法交叉驗證結果
3. **持續學習**：統計方法不斷發展，需要持續更新知識
4. **實際應用**：理論結合實際，在實戰中檢驗方法效果
5. **謹慎解釋**：正確理解統計結果的含義和局限性

*注意：統計分析僅供研究參考，不保證預測準確性。*
