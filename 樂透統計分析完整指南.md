# 樂透統計分析完整指南
*採用一般統計方法的系統性分析*

## 目錄
1. [統計學基礎理論](#統計學基礎理論)
2. [數據收集與預處理](#數據收集與預處理)
3. [描述性統計分析](#描述性統計分析)
4. [機率分佈分析](#機率分佈分析)
5. [假設檢驗方法](#假設檢驗方法)
6. [相關性與回歸分析](#相關性與回歸分析)
7. [時間序列分析](#時間序列分析)
8. [實用分析工具](#實用分析工具)
9. [結果解釋與應用](#結果解釋與應用)

## 統計學基礎理論

### 樂透的數學本質
```
大樂透基本參數：
- 號碼範圍：1-49
- 選取數量：6個主號碼 + 1個特別號
- 總組合數：C(49,6) = 13,983,816
- 理論中獎機率：1/13,983,816 ≈ 0.0000071%
```

### 核心統計概念

#### 1. 機率論基礎
- **等機率假設**：每個號碼被抽中的機率相等 (1/49)
- **獨立性假設**：每期開獎結果相互獨立
- **隨機性原理**：過去結果不影響未來結果

#### 2. 大數定律應用
```
當樣本數 n → ∞ 時：
實際頻率 → 理論機率
每個號碼的期望出現次數 = 總期數 × 6/49
```

#### 3. 中央極限定理
```
樣本均值的分佈：
X̄ ~ N(μ, σ²/n)
其中 μ = 25 (號碼平均值), σ² = 號碼變異數
```

## 數據收集與預處理

### 數據來源標準
1. **官方數據**：台灣彩券公司開獎記錄
2. **數據完整性**：確保無遺漏、無錯誤
3. **時間範圍**：建議至少500期以上的歷史數據

### 數據結構設計
```python
# 標準數據格式
lottery_data = {
    'period': '期數',
    'date': '開獎日期',
    'numbers': [n1, n2, n3, n4, n5, n6],  # 主號碼（已排序）
    'special': n7,  # 特別號
    'sum_value': sum(numbers),  # 和值
    'odd_count': count_odd(numbers),  # 奇數個數
    'big_count': count_big(numbers)  # 大數個數（>25）
}
```

### 數據清洗步驟
1. **格式統一**：確保號碼格式一致
2. **範圍檢查**：號碼必須在1-49範圍內
3. **重複檢查**：同期內號碼不能重複
4. **排序處理**：主號碼按升序排列
5. **缺失值處理**：標記或補充缺失數據

## 描述性統計分析

### 1. 中心趨勢測量

#### 號碼頻率分析
```python
# 計算每個號碼的出現頻率
frequency = {}
for number in range(1, 50):
    frequency[number] = count_appearances(number) / total_periods

# 統計指標
mean_frequency = sum(frequency.values()) / 49
median_frequency = median(frequency.values())
mode_frequency = max(frequency.values())
```

#### 和值統計
```python
# 和值的描述性統計
sum_values = [sum(period_numbers) for period_numbers in all_periods]
sum_mean = mean(sum_values)  # 理論值約150
sum_std = std(sum_values)
sum_range = max(sum_values) - min(sum_values)
```

### 2. 離散程度測量

#### 變異係數分析
```python
# 號碼出現頻率的變異係數
cv = std(frequencies) / mean(frequencies)
# CV值接近0表示分佈均勻，符合隨機性
```

#### 四分位數分析
```python
# 號碼頻率的四分位數
Q1 = percentile(frequencies, 25)
Q2 = percentile(frequencies, 50)  # 中位數
Q3 = percentile(frequencies, 75)
IQR = Q3 - Q1  # 四分位距
```

### 3. 分佈形狀測量

#### 偏度分析
```python
# 頻率分佈的偏度
skewness = calculate_skewness(frequencies)
# 偏度接近0表示對稱分佈
```

#### 峰度分析
```python
# 頻率分佈的峰度
kurtosis = calculate_kurtosis(frequencies)
# 峰度接近3表示正態分佈
```

## 機率分佈分析

### 1. 均勻分佈檢驗
```python
# 檢驗號碼出現是否符合均勻分佈
from scipy.stats import chisquare

observed_freq = [count_number(i) for i in range(1, 50)]
expected_freq = [total_periods * 6 / 49] * 49

chi2_stat, p_value = chisquare(observed_freq, expected_freq)
# p > 0.05 表示符合均勻分佈
```

### 2. 正態分佈檢驗
```python
# 檢驗和值是否符合正態分佈
from scipy.stats import shapiro, normaltest

# Shapiro-Wilk檢驗
stat, p_value = shapiro(sum_values)

# D'Agostino檢驗
stat, p_value = normaltest(sum_values)
```

### 3. 泊松分佈檢驗
```python
# 檢驗號碼間隔是否符合泊松分佈
from scipy.stats import poisson

intervals = calculate_intervals_between_appearances()
lambda_param = mean(intervals)

# 擬合優度檢驗
observed_freq = histogram(intervals)
expected_freq = [poisson.pmf(k, lambda_param) * len(intervals) 
                 for k in range(max(intervals)+1)]
```

## 假設檢驗方法

### 1. 獨立性檢驗
```python
# 檢驗相鄰期數是否獨立
from scipy.stats import chi2_contingency

# 建立列聯表
contingency_table = create_contingency_table(current_period, next_period)
chi2, p_value, dof, expected = chi2_contingency(contingency_table)
```

### 2. 隨機性檢驗
```python
# 遊程檢驗（Runs Test）
def runs_test(sequence):
    """檢驗序列的隨機性"""
    runs = count_runs(sequence)
    n1 = sequence.count(0)  # 一種結果的數量
    n2 = sequence.count(1)  # 另一種結果的數量
    
    expected_runs = (2 * n1 * n2) / (n1 + n2) + 1
    variance = (2 * n1 * n2 * (2 * n1 * n2 - n1 - n2)) / \
               ((n1 + n2) ** 2 * (n1 + n2 - 1))
    
    z_score = (runs - expected_runs) / sqrt(variance)
    return z_score, p_value_from_z(z_score)
```

### 3. 趨勢檢驗
```python
# Mann-Kendall趨勢檢驗
from scipy.stats import kendalltau

def mann_kendall_test(data):
    """檢驗時間序列是否存在趨勢"""
    n = len(data)
    s = 0
    
    for i in range(n-1):
        for j in range(i+1, n):
            if data[j] > data[i]:
                s += 1
            elif data[j] < data[i]:
                s -= 1
    
    # 計算統計量和p值
    var_s = n * (n - 1) * (2 * n + 5) / 18
    z = s / sqrt(var_s)
    
    return z, p_value_from_z(z)
```

## 相關性與回歸分析

### 1. 號碼間相關性
```python
# 計算號碼間的相關係數矩陣
import pandas as pd

# 建立號碼出現矩陣（每行一期，每列一個號碼位置）
number_matrix = create_number_matrix(lottery_data)
correlation_matrix = number_matrix.corr()

# 識別強相關的號碼對
strong_correlations = find_strong_correlations(correlation_matrix, threshold=0.1)
```

### 2. 時間相關性
```python
# 自相關分析
from statsmodels.tsa.stattools import acf, pacf

# 計算號碼出現的自相關函數
for number in range(1, 50):
    number_series = create_time_series(number, lottery_data)
    autocorr = acf(number_series, nlags=20)
    partial_autocorr = pacf(number_series, nlags=20)
```

### 3. 多元回歸分析
```python
# 使用歷史數據預測號碼出現機率
from sklearn.linear_model import LinearRegression

# 特徵工程
features = create_features(lottery_data)  # 包含歷史頻率、間隔等
target = create_target(lottery_data)      # 下期號碼出現情況

# 建立回歸模型
model = LinearRegression()
model.fit(features, target)

# 評估模型
r2_score = model.score(features, target)
coefficients = model.coef_
```

## 時間序列分析

### 1. 趨勢分解
```python
# 使用移動平均分解趨勢
def moving_average_trend(data, window=10):
    """計算移動平均趨勢"""
    return data.rolling(window=window).mean()

# 季節性分解
from statsmodels.tsa.seasonal import seasonal_decompose

for number in range(1, 50):
    series = create_time_series(number, lottery_data)
    decomposition = seasonal_decompose(series, model='additive', period=52)
    trend = decomposition.trend
    seasonal = decomposition.seasonal
    residual = decomposition.resid
```

### 2. 週期性分析
```python
# 傅立葉變換識別週期
import numpy as np

def find_periodicity(series):
    """使用FFT識別週期性"""
    fft = np.fft.fft(series)
    frequencies = np.fft.fftfreq(len(series))
    
    # 找出主要頻率
    power_spectrum = np.abs(fft) ** 2
    dominant_freq = frequencies[np.argmax(power_spectrum[1:]) + 1]
    period = 1 / abs(dominant_freq) if dominant_freq != 0 else None
    
    return period
```

### 3. 狀態轉移分析
```python
# 馬可夫鏈分析
def create_transition_matrix(sequence, states):
    """建立狀態轉移矩陣"""
    n_states = len(states)
    transition_matrix = np.zeros((n_states, n_states))
    
    for i in range(len(sequence) - 1):
        current_state = states.index(sequence[i])
        next_state = states.index(sequence[i + 1])
        transition_matrix[current_state][next_state] += 1
    
    # 正規化為機率
    for i in range(n_states):
        row_sum = transition_matrix[i].sum()
        if row_sum > 0:
            transition_matrix[i] /= row_sum
    
    return transition_matrix
```

## 實用分析工具

### 1. 熱度分析工具
```python
class HeatAnalyzer:
    """號碼熱度分析器"""
    
    def __init__(self, data, window=20):
        self.data = data
        self.window = window
    
    def calculate_heat_score(self, number):
        """計算號碼熱度分數"""
        recent_appearances = self.get_recent_appearances(number)
        frequency_score = len(recent_appearances) / self.window
        
        # 考慮時間衰減
        time_weights = [0.9 ** i for i in range(len(recent_appearances))]
        weighted_score = sum(time_weights) / self.window
        
        return frequency_score * 0.7 + weighted_score * 0.3
    
    def get_hot_numbers(self, top_n=10):
        """獲取熱門號碼"""
        heat_scores = {i: self.calculate_heat_score(i) for i in range(1, 50)}
        return sorted(heat_scores.items(), key=lambda x: x[1], reverse=True)[:top_n]
```

### 2. 間隔分析工具
```python
class IntervalAnalyzer:
    """間隔分析器"""
    
    def calculate_intervals(self, number):
        """計算號碼出現間隔"""
        appearances = self.find_appearances(number)
        intervals = [appearances[i+1] - appearances[i] 
                    for i in range(len(appearances)-1)]
        return intervals
    
    def predict_next_appearance(self, number):
        """預測下次出現期數"""
        intervals = self.calculate_intervals(number)
        avg_interval = np.mean(intervals)
        std_interval = np.std(intervals)
        
        last_appearance = self.get_last_appearance(number)
        current_interval = self.current_period - last_appearance
        
        # 基於統計的預測
        if current_interval > avg_interval + std_interval:
            return "即將出現"
        elif current_interval < avg_interval - std_interval:
            return "剛剛出現"
        else:
            return f"預計 {int(avg_interval - current_interval)} 期後出現"
```

### 3. 組合分析工具
```python
class CombinationAnalyzer:
    """組合分析器"""
    
    def analyze_sum_distribution(self):
        """分析和值分佈"""
        sums = [sum(period['numbers']) for period in self.data]
        
        return {
            'mean': np.mean(sums),
            'std': np.std(sums),
            'min': min(sums),
            'max': max(sums),
            'most_common_range': self.find_most_common_range(sums)
        }
    
    def analyze_odd_even_pattern(self):
        """分析奇偶模式"""
        patterns = []
        for period in self.data:
            odd_count = sum(1 for n in period['numbers'] if n % 2 == 1)
            patterns.append(odd_count)
        
        pattern_freq = {i: patterns.count(i) for i in range(7)}
        return pattern_freq
    
    def analyze_consecutive_numbers(self):
        """分析連號情況"""
        consecutive_counts = []
        for period in self.data:
            numbers = sorted(period['numbers'])
            consecutive = self.count_consecutive(numbers)
            consecutive_counts.append(consecutive)
        
        return {
            'average': np.mean(consecutive_counts),
            'distribution': {i: consecutive_counts.count(i) 
                           for i in range(max(consecutive_counts)+1)}
        }
```

## 結果解釋與應用

### 統計結果的正確解釋

#### 1. 頻率分析結果
- **熱門號碼**：出現頻率高於平均值，但不代表未來更容易出現
- **冷門號碼**：出現頻率低於平均值，根據大數定律，長期會趨向平均
- **變異係數**：接近0表示分佈均勻，符合隨機性假設

#### 2. 假設檢驗結果
- **p值 > 0.05**：接受原假設，數據符合隨機性
- **p值 ≤ 0.05**：拒絕原假設，可能存在非隨機模式
- **注意**：統計顯著不等於實際意義

#### 3. 相關性分析結果
- **相關係數接近0**：號碼間無線性關係，符合獨立性
- **強相關**：可能是偶然現象，需要更多數據驗證
- **時間相關性**：檢驗是否存在週期性模式

### 實際應用建議

#### 1. 理性認知
```
重要提醒：
- 樂透本質上是隨機事件
- 統計分析無法改變中獎機率
- 過去結果不影響未來結果
- 任何"必勝法"都是錯誤的
```

#### 2. 統計分析的價值
- **學術研究**：驗證隨機性理論
- **數據科學**：練習統計方法
- **娛樂參考**：增加選號樂趣（但不影響中獎率）

#### 3. 風險控制
- **設定預算**：只用可承受損失的金額
- **理性投注**：不要因統計結果增加投注
- **娛樂心態**：將其視為娛樂而非投資

### 結論

樂透統計分析是一個很好的統計學習案例，但必須認清其局限性：

1. **統計方法正確性**：可以驗證數據的隨機性
2. **預測能力限制**：無法提高中獎機率
3. **教育價值**：幫助理解機率和統計概念
4. **娛樂功能**：增加參與樂趣，但不改變本質

記住：**最好的樂透策略就是不把它當作投資，而是當作娛樂。**

---

*本指南僅供學術研究和統計學習使用，不鼓勵過度投注。請理性參與，量力而為。*
