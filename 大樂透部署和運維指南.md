# 大樂透分析系統部署和運維指南

## 目錄
1. [系統概述](#系統概述)
2. [環境準備](#環境準備)
3. [容器化部署](#容器化部署)
4. [CI/CD流程](#cicd流程)
5. [監控和日誌](#監控和日誌)
6. [安全性配置](#安全性配置)
7. [性能優化](#性能優化)
8. [故障排除](#故障排除)

## 系統概述

### 架構組件
- **核心分析引擎**: Python-based統計分析工具
- **數據處理層**: Pandas + NumPy數據處理
- **可視化層**: Matplotlib + Plotly圖表生成
- **機器學習**: Scikit-learn模型訓練
- **Web界面**: Flask/FastAPI (可選)
- **數據庫**: SQLite/PostgreSQL (可選)

### 技術棧
```
Python 3.8+
├── pandas >= 1.3.0
├── numpy >= 1.20.0
├── matplotlib >= 3.3.0
├── seaborn >= 0.11.0
├── scipy >= 1.7.0
├── scikit-learn >= 1.0.0
├── statsmodels >= 0.13.0
├── plotly >= 5.0.0
└── jupyter >= 1.0.0
```

## 環境準備

### 1. 系統要求
```bash
# 最低配置
CPU: 2核心
RAM: 4GB
存儲: 10GB
OS: Linux/Windows/macOS

# 推薦配置
CPU: 4核心
RAM: 8GB
存儲: 50GB SSD
OS: Ubuntu 20.04 LTS
```

### 2. Python環境設置
```bash
# 安裝Python 3.8+
sudo apt update
sudo apt install python3.8 python3.8-venv python3.8-dev

# 創建虛擬環境
python3.8 -m venv lottery_env
source lottery_env/bin/activate

# 安裝依賴
pip install -r requirements.txt
```

### 3. 系統依賴
```bash
# Ubuntu/Debian
sudo apt install build-essential
sudo apt install python3-tk  # matplotlib GUI支持
sudo apt install fonts-wqy-zenhei  # 中文字體支持

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install tkinter
sudo yum install wqy-zenhei-fonts
```

## 容器化部署

### 1. Dockerfile
```dockerfile
FROM python:3.8-slim

# 設置工作目錄
WORKDIR /app

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    build-essential \
    python3-tk \
    fonts-wqy-zenhei \
    && rm -rf /var/lib/apt/lists/*

# 複製依賴文件
COPY requirements.txt .

# 安裝Python依賴
RUN pip install --no-cache-dir -r requirements.txt

# 複製應用代碼
COPY . .

# 設置環境變量
ENV PYTHONPATH=/app
ENV MPLBACKEND=Agg

# 暴露端口（如果有Web界面）
EXPOSE 8000

# 啟動命令
CMD ["python", "大樂透統計分析工具.py"]
```

### 2. Docker Compose
```yaml
version: '3.8'

services:
  lottery-analyzer:
    build: .
    container_name: lottery-analyzer
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./output:/app/output
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
    restart: unless-stopped
    
  # 可選: Web界面
  lottery-web:
    build:
      context: .
      dockerfile: Dockerfile.web
    container_name: lottery-web
    ports:
      - "8000:8000"
    depends_on:
      - lottery-analyzer
    environment:
      - FLASK_ENV=production
    restart: unless-stopped
    
  # 可選: 數據庫
  postgres:
    image: postgres:13
    container_name: lottery-db
    environment:
      POSTGRES_DB: lottery
      POSTGRES_USER: lottery_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data:
```

### 3. 構建和運行
```bash
# 構建鏡像
docker build -t lottery-analyzer:latest .

# 運行容器
docker-compose up -d

# 查看日誌
docker-compose logs -f lottery-analyzer

# 停止服務
docker-compose down
```

## CI/CD流程

### 1. GitHub Actions工作流
```yaml
name: Lottery Analyzer CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov
        
    - name: Run tests
      run: |
        pytest tests/ --cov=./ --cov-report=xml
        
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      
  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Build Docker image
      run: |
        docker build -t lottery-analyzer:${{ github.sha }} .
        
    - name: Push to registry
      if: github.ref == 'refs/heads/main'
      run: |
        echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
        docker push lottery-analyzer:${{ github.sha }}
        
  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to production
      run: |
        # 部署腳本
        ssh user@server "docker pull lottery-analyzer:${{ github.sha }} && docker-compose up -d"
```

### 2. 部署腳本
```bash
#!/bin/bash
# deploy.sh

set -e

echo "開始部署大樂透分析系統..."

# 拉取最新代碼
git pull origin main

# 構建新鏡像
docker build -t lottery-analyzer:latest .

# 停止舊容器
docker-compose down

# 啟動新容器
docker-compose up -d

# 健康檢查
sleep 30
if docker ps | grep -q lottery-analyzer; then
    echo "✅ 部署成功！"
else
    echo "❌ 部署失敗！"
    exit 1
fi

echo "部署完成！"
```

## 監控和日誌

### 1. 日誌配置
```python
# logging_config.py
import logging
import logging.handlers
import os

def setup_logging():
    """設置日誌配置"""
    
    # 創建日誌目錄
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    # 配置日誌格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 文件處理器
    file_handler = logging.handlers.RotatingFileHandler(
        f"{log_dir}/lottery_analyzer.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(formatter)
    
    # 控制台處理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 根日誌器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return root_logger
```

### 2. 性能監控
```python
# monitoring.py
import time
import psutil
import logging
from functools import wraps

def monitor_performance(func):
    """性能監控裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        try:
            result = func(*args, **kwargs)
            
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            logging.info(f"{func.__name__} 執行完成:")
            logging.info(f"  執行時間: {end_time - start_time:.2f}秒")
            logging.info(f"  內存使用: {end_memory:.2f}MB")
            logging.info(f"  內存變化: {end_memory - start_memory:+.2f}MB")
            
            return result
            
        except Exception as e:
            logging.error(f"{func.__name__} 執行失敗: {str(e)}")
            raise
            
    return wrapper
```

### 3. 健康檢查
```python
# health_check.py
import requests
import logging
from datetime import datetime

def health_check():
    """系統健康檢查"""
    
    checks = {
        "timestamp": datetime.now().isoformat(),
        "status": "healthy",
        "checks": {}
    }
    
    # 檢查內存使用
    memory_percent = psutil.virtual_memory().percent
    checks["checks"]["memory"] = {
        "status": "ok" if memory_percent < 80 else "warning",
        "usage_percent": memory_percent
    }
    
    # 檢查磁盤空間
    disk_percent = psutil.disk_usage('/').percent
    checks["checks"]["disk"] = {
        "status": "ok" if disk_percent < 90 else "warning",
        "usage_percent": disk_percent
    }
    
    # 檢查分析工具
    try:
        from 大樂透統計分析工具 import LotteryAnalyzer
        analyzer = LotteryAnalyzer()
        checks["checks"]["analyzer"] = {"status": "ok"}
    except Exception as e:
        checks["checks"]["analyzer"] = {
            "status": "error",
            "error": str(e)
        }
        checks["status"] = "unhealthy"
    
    return checks
```

## 安全性配置

### 1. 環境變量管理
```bash
# .env
DATABASE_URL=postgresql://user:password@localhost:5432/lottery
SECRET_KEY=your-secret-key-here
LOG_LEVEL=INFO
ALLOWED_HOSTS=localhost,127.0.0.1
```

### 2. 數據加密
```python
# security.py
import os
from cryptography.fernet import Fernet

class DataEncryption:
    def __init__(self):
        key = os.environ.get('ENCRYPTION_KEY')
        if not key:
            key = Fernet.generate_key()
            print(f"生成新的加密密鑰: {key.decode()}")
        else:
            key = key.encode()
        self.cipher = Fernet(key)
    
    def encrypt_data(self, data):
        """加密數據"""
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt_data(self, encrypted_data):
        """解密數據"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()
```

### 3. 訪問控制
```python
# auth.py
import jwt
import hashlib
from datetime import datetime, timedelta

class AuthManager:
    def __init__(self, secret_key):
        self.secret_key = secret_key
    
    def hash_password(self, password):
        """密碼哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def generate_token(self, user_id):
        """生成JWT令牌"""
        payload = {
            'user_id': user_id,
            'exp': datetime.utcnow() + timedelta(hours=24)
        }
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
    
    def verify_token(self, token):
        """驗證JWT令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            return payload['user_id']
        except jwt.ExpiredSignatureError:
            return None
```

## 性能優化

### 1. 數據處理優化
```python
# optimization.py
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp

class OptimizedAnalyzer:
    def __init__(self, n_workers=None):
        self.n_workers = n_workers or mp.cpu_count()
    
    def parallel_analysis(self, data_chunks):
        """並行數據分析"""
        with ThreadPoolExecutor(max_workers=self.n_workers) as executor:
            futures = [
                executor.submit(self._analyze_chunk, chunk)
                for chunk in data_chunks
            ]
            results = [future.result() for future in futures]
        return results
    
    def _analyze_chunk(self, chunk):
        """分析數據塊"""
        # 實現具體的分析邏輯
        pass
    
    def optimize_memory(self, df):
        """內存優化"""
        for col in df.columns:
            if df[col].dtype == 'int64':
                if df[col].min() >= 0 and df[col].max() <= 255:
                    df[col] = df[col].astype('uint8')
                elif df[col].min() >= -128 and df[col].max() <= 127:
                    df[col] = df[col].astype('int8')
        return df
```

### 2. 緩存策略
```python
# cache.py
import redis
import pickle
import hashlib
from functools import wraps

class CacheManager:
    def __init__(self, redis_url='redis://localhost:6379'):
        self.redis_client = redis.from_url(redis_url)
    
    def cache_result(self, expire_time=3600):
        """結果緩存裝飾器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # 生成緩存鍵
                cache_key = self._generate_cache_key(func.__name__, args, kwargs)
                
                # 嘗試從緩存獲取
                cached_result = self.redis_client.get(cache_key)
                if cached_result:
                    return pickle.loads(cached_result)
                
                # 執行函數並緩存結果
                result = func(*args, **kwargs)
                self.redis_client.setex(
                    cache_key, 
                    expire_time, 
                    pickle.dumps(result)
                )
                
                return result
            return wrapper
        return decorator
    
    def _generate_cache_key(self, func_name, args, kwargs):
        """生成緩存鍵"""
        key_data = f"{func_name}:{str(args)}:{str(kwargs)}"
        return hashlib.md5(key_data.encode()).hexdigest()
```

## 故障排除

### 常見問題和解決方案

#### 1. 內存不足
```bash
# 問題: 分析大數據集時內存溢出
# 解決方案:
# 1. 增加系統內存
# 2. 使用數據分塊處理
# 3. 優化數據類型
# 4. 使用生成器而非列表
```

#### 2. 依賴衝突
```bash
# 問題: Python包版本衝突
# 解決方案:
pip install --upgrade pip
pip install --force-reinstall -r requirements.txt

# 或使用conda環境
conda create -n lottery python=3.8
conda activate lottery
conda install --file requirements.txt
```

#### 3. 圖表顯示問題
```python
# 問題: matplotlib圖表不顯示
# 解決方案:
import matplotlib
matplotlib.use('Agg')  # 使用非交互式後端
import matplotlib.pyplot as plt

# 或設置環境變量
export MPLBACKEND=Agg
```

#### 4. 性能問題
```python
# 問題: 分析速度慢
# 解決方案:
# 1. 使用向量化操作
df['result'] = df['col1'] * df['col2']  # 而非循環

# 2. 使用適當的數據結構
# 3. 啟用並行處理
# 4. 使用緩存
```

### 監控指標
- CPU使用率 < 80%
- 內存使用率 < 80%
- 磁盤使用率 < 90%
- 響應時間 < 5秒
- 錯誤率 < 1%

---

*注意: 本指南提供了完整的部署和運維方案，請根據實際環境調整配置參數。*
