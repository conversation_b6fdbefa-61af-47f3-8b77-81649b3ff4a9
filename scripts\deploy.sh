#!/bin/bash
# 大樂透分析系統部署腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查依賴
check_dependencies() {
    log_info "檢查系統依賴..."
    
    # 檢查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安裝，請先安裝Docker"
        exit 1
    fi
    
    # 檢查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安裝，請先安裝Docker Compose"
        exit 1
    fi
    
    # 檢查Git
    if ! command -v git &> /dev/null; then
        log_error "Git未安裝，請先安裝Git"
        exit 1
    fi
    
    log_success "依賴檢查完成"
}

# 備份現有數據
backup_data() {
    log_info "備份現有數據..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 備份數據庫
    if docker ps | grep -q lottery-db; then
        log_info "備份數據庫..."
        docker exec lottery-db pg_dump -U lottery_user lottery > "$BACKUP_DIR/database.sql"
    fi
    
    # 備份數據文件
    if [ -d "data" ]; then
        log_info "備份數據文件..."
        cp -r data "$BACKUP_DIR/"
    fi
    
    # 備份配置文件
    if [ -d "config" ]; then
        log_info "備份配置文件..."
        cp -r config "$BACKUP_DIR/"
    fi
    
    log_success "數據備份完成: $BACKUP_DIR"
}

# 拉取最新代碼
pull_latest_code() {
    log_info "拉取最新代碼..."
    
    # 檢查是否有未提交的更改
    if [ -n "$(git status --porcelain)" ]; then
        log_warning "檢測到未提交的更改，正在暫存..."
        git stash
    fi
    
    # 拉取最新代碼
    git pull origin main
    
    log_success "代碼更新完成"
}

# 構建Docker鏡像
build_images() {
    log_info "構建Docker鏡像..."
    
    # 構建主應用鏡像
    docker build -t lottery-analyzer:latest .
    
    # 構建Web界面鏡像（如果存在）
    if [ -f "Dockerfile.web" ]; then
        docker build -f Dockerfile.web -t lottery-web:latest .
    fi
    
    # 構建Jupyter鏡像（如果存在）
    if [ -f "Dockerfile.jupyter" ]; then
        docker build -f Dockerfile.jupyter -t lottery-jupyter:latest .
    fi
    
    log_success "鏡像構建完成"
}

# 運行測試
run_tests() {
    log_info "運行測試..."
    
    # 創建測試容器
    docker run --rm \
        -v "$(pwd):/app" \
        -w /app \
        lottery-analyzer:latest \
        python -m pytest tests/ -v
    
    if [ $? -eq 0 ]; then
        log_success "所有測試通過"
    else
        log_error "測試失敗，部署中止"
        exit 1
    fi
}

# 停止舊服務
stop_old_services() {
    log_info "停止舊服務..."
    
    docker-compose down --remove-orphans
    
    log_success "舊服務已停止"
}

# 啟動新服務
start_new_services() {
    log_info "啟動新服務..."
    
    # 設置環境變量
    export POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-$(openssl rand -base64 32)}
    export REDIS_PASSWORD=${REDIS_PASSWORD:-$(openssl rand -base64 32)}
    export JUPYTER_TOKEN=${JUPYTER_TOKEN:-$(openssl rand -base64 32)}
    
    # 啟動核心服務
    docker-compose up -d lottery-analyzer postgres redis
    
    # 等待服務啟動
    log_info "等待服務啟動..."
    sleep 30
    
    # 啟動Web服務（如果配置了）
    if [ "$ENABLE_WEB" = "true" ]; then
        docker-compose up -d lottery-web nginx
    fi
    
    # 啟動監控服務（如果配置了）
    if [ "$ENABLE_MONITORING" = "true" ]; then
        docker-compose --profile monitoring up -d
    fi
    
    log_success "新服務已啟動"
}

# 健康檢查
health_check() {
    log_info "執行健康檢查..."
    
    # 檢查核心服務
    if ! docker ps | grep -q lottery-analyzer; then
        log_error "核心分析服務未運行"
        return 1
    fi
    
    # 檢查數據庫
    if ! docker exec lottery-db pg_isready -U lottery_user -d lottery; then
        log_error "數據庫連接失敗"
        return 1
    fi
    
    # 檢查Redis
    if ! docker exec lottery-redis redis-cli ping; then
        log_error "Redis連接失敗"
        return 1
    fi
    
    # 檢查Web服務（如果啟用）
    if [ "$ENABLE_WEB" = "true" ]; then
        if ! curl -f http://localhost:8000/health &> /dev/null; then
            log_warning "Web服務健康檢查失敗"
        fi
    fi
    
    log_success "健康檢查通過"
}

# 清理舊鏡像
cleanup_old_images() {
    log_info "清理舊Docker鏡像..."
    
    # 清理未使用的鏡像
    docker image prune -f
    
    # 清理未使用的容器
    docker container prune -f
    
    # 清理未使用的網絡
    docker network prune -f
    
    log_success "清理完成"
}

# 顯示部署信息
show_deployment_info() {
    log_success "🎉 部署完成！"
    echo ""
    echo "服務信息:"
    echo "- 核心分析服務: 運行中"
    echo "- 數據庫: PostgreSQL (端口 5432)"
    echo "- 緩存: Redis (端口 6379)"
    
    if [ "$ENABLE_WEB" = "true" ]; then
        echo "- Web界面: http://localhost:8000"
    fi
    
    if [ "$ENABLE_MONITORING" = "true" ]; then
        echo "- 監控面板: http://localhost:3000 (Grafana)"
        echo "- 指標收集: http://localhost:9090 (Prometheus)"
    fi
    
    echo ""
    echo "管理命令:"
    echo "- 查看日誌: docker-compose logs -f"
    echo "- 停止服務: docker-compose down"
    echo "- 重啟服務: docker-compose restart"
    echo ""
}

# 主函數
main() {
    echo "🚀 開始部署大樂透分析系統..."
    echo ""
    
    # 解析命令行參數
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --enable-web)
                ENABLE_WEB=true
                shift
                ;;
            --enable-monitoring)
                ENABLE_MONITORING=true
                shift
                ;;
            --no-backup)
                NO_BACKUP=true
                shift
                ;;
            --help)
                echo "用法: $0 [選項]"
                echo "選項:"
                echo "  --skip-tests        跳過測試"
                echo "  --enable-web        啟用Web界面"
                echo "  --enable-monitoring 啟用監控服務"
                echo "  --no-backup         跳過數據備份"
                echo "  --help              顯示幫助信息"
                exit 0
                ;;
            *)
                log_error "未知選項: $1"
                exit 1
                ;;
        esac
    done
    
    # 執行部署步驟
    check_dependencies
    
    if [ "$NO_BACKUP" != "true" ]; then
        backup_data
    fi
    
    pull_latest_code
    build_images
    
    if [ "$SKIP_TESTS" != "true" ]; then
        run_tests
    fi
    
    stop_old_services
    start_new_services
    
    # 等待服務完全啟動
    sleep 10
    
    health_check
    cleanup_old_images
    show_deployment_info
}

# 錯誤處理
trap 'log_error "部署過程中發生錯誤，請檢查日誌"; exit 1' ERR

# 執行主函數
main "$@"
