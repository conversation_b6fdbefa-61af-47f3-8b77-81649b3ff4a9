# 大樂透統計分析快速參考

## 快速導航
- [基礎統計](#基礎統計)
- [頻率分析](#頻率分析)
- [分佈分析](#分佈分析)
- [相關性分析](#相關性分析)
- [假設檢驗](#假設檢驗)
- [時間序列](#時間序列)
- [模式識別](#模式識別)
- [預測方法](#預測方法)

## 基礎統計

### 描述性統計
```python
# 中心趨勢
mean = data.mean()           # 平均數
median = data.median()       # 中位數
mode = data.mode()           # 眾數

# 變異性
std = data.std()             # 標準差
var = data.var()             # 變異數
cv = std / mean              # 變異係數
```

### 分位數分析
```python
q25 = data.quantile(0.25)    # 第一四分位數
q75 = data.quantile(0.75)    # 第三四分位數
iqr = q75 - q25              # 四分位距
```

## 頻率分析

### 出現頻率統計
```python
# 計算頻率
frequency = data.value_counts()
relative_freq = frequency / len(data)

# 識別熱冷號
hot_threshold = frequency.mean() + frequency.std()
cold_threshold = frequency.mean() - frequency.std()
hot_numbers = frequency[frequency > hot_threshold]
cold_numbers = frequency[frequency < cold_threshold]
```

### 期望頻率比較
```python
expected_freq = total_periods * 6 / 49
deviation = frequency - expected_freq
deviation_rate = deviation / expected_freq
```

## 分佈分析

### 奇偶分佈
```python
def analyze_odd_even(numbers):
    odd_count = sum(1 for n in numbers if n % 2 == 1)
    return odd_count, 6 - odd_count

# 統計各期奇偶比例
odd_even_dist = [analyze_odd_even(row) for row in data]
```

### 大小分佈
```python
def analyze_big_small(numbers, threshold=25):
    big_count = sum(1 for n in numbers if n > threshold)
    return big_count, 6 - big_count

# 統計各期大小比例
big_small_dist = [analyze_big_small(row) for row in data]
```

### 和值分析
```python
sum_values = data.sum(axis=1)
sum_mean = sum_values.mean()
sum_std = sum_values.std()
sum_range = (sum_values.min(), sum_values.max())
```

## 相關性分析

### 皮爾森相關
```python
import scipy.stats as stats

# 計算相關係數
correlation_matrix = data.corr()
corr_coef, p_value = stats.pearsonr(x, y)
```

### 斯皮爾曼等級相關
```python
spearman_corr, p_value = stats.spearmanr(x, y)
```

### 肯德爾τ相關
```python
kendall_tau, p_value = stats.kendalltau(x, y)
```

## 假設檢驗

### 均勻性檢驗（卡方檢驗）
```python
from scipy.stats import chisquare

observed = frequency.values
expected = [len(data) * 6 / 49] * 49
chi2_stat, p_value = chisquare(observed, expected)

# 判斷：p > 0.05 表示符合均勻分佈
is_uniform = p_value > 0.05
```

### 正態性檢驗
```python
from scipy.stats import shapiro, normaltest

# Shapiro-Wilk檢驗
stat, p_value = shapiro(data)

# D'Agostino檢驗
stat, p_value = normaltest(data)

# 判斷：p > 0.05 表示符合正態分佈
is_normal = p_value > 0.05
```

### 獨立性檢驗
```python
# 檢驗相鄰期數的獨立性
current = data[1:]
previous = data[:-1]
corr, p_value = stats.pearsonr(current, previous)

# 判斷：p > 0.05 表示獨立
is_independent = p_value > 0.05
```

## 時間序列

### 移動平均
```python
def moving_average(data, window):
    return data.rolling(window=window).mean()

# 計算不同週期的移動平均
ma_5 = moving_average(data, 5)
ma_10 = moving_average(data, 10)
ma_20 = moving_average(data, 20)
```

### 趨勢分析
```python
from scipy.stats import linregress

# 線性趨勢
x = range(len(data))
slope, intercept, r_value, p_value, std_err = linregress(x, data)

# 趨勢方向
trend = "上升" if slope > 0 else "下降" if slope < 0 else "平穩"
```

### 季節性分解
```python
from statsmodels.tsa.seasonal import seasonal_decompose

# 分解時間序列
decomposition = seasonal_decompose(data, model='additive', period=12)
trend = decomposition.trend
seasonal = decomposition.seasonal
residual = decomposition.resid
```

## 模式識別

### 連號分析
```python
def count_consecutive(numbers):
    numbers = sorted(numbers)
    max_consecutive = 0
    current_consecutive = 1
    
    for i in range(1, len(numbers)):
        if numbers[i] == numbers[i-1] + 1:
            current_consecutive += 1
            max_consecutive = max(max_consecutive, current_consecutive)
        else:
            current_consecutive = 1
    
    return max_consecutive
```

### AC值計算
```python
def calculate_ac_value(numbers):
    numbers = sorted(numbers)
    differences = []
    
    for i in range(len(numbers)):
        for j in range(i+1, len(numbers)):
            differences.append(abs(numbers[i] - numbers[j]))
    
    unique_differences = len(set(differences))
    ac_value = unique_differences - 5
    return ac_value
```

### 重複號分析
```python
def count_repeats(current_numbers, previous_numbers):
    current_set = set(current_numbers)
    previous_set = set(previous_numbers)
    return len(current_set.intersection(previous_set))
```

## 預測方法

### 機率預測
```python
def probability_prediction(frequency_data, top_n=10):
    # 基於頻率的機率預測
    probabilities = frequency_data / frequency_data.sum()
    top_numbers = probabilities.nlargest(top_n)
    return top_numbers
```

### 回歸預測
```python
from sklearn.linear_model import LinearRegression

def regression_prediction(X, y, future_periods=1):
    model = LinearRegression()
    model.fit(X, y)
    
    # 預測未來期數
    future_X = [[len(X) + i] for i in range(1, future_periods + 1)]
    predictions = model.predict(future_X)
    return predictions
```

### 馬可夫鏈預測
```python
def markov_chain_prediction(sequence, order=1):
    # 建立轉移矩陣
    transitions = {}
    
    for i in range(len(sequence) - order):
        state = tuple(sequence[i:i+order])
        next_state = sequence[i+order]
        
        if state not in transitions:
            transitions[state] = {}
        if next_state not in transitions[state]:
            transitions[state][next_state] = 0
        transitions[state][next_state] += 1
    
    # 計算轉移機率
    for state in transitions:
        total = sum(transitions[state].values())
        for next_state in transitions[state]:
            transitions[state][next_state] /= total
    
    return transitions
```

## 實用工具函數

### 數據驗證
```python
def validate_lottery_data(data):
    """驗證彩票數據的有效性"""
    errors = []
    
    # 檢查號碼範圍
    if (data < 1).any().any() or (data > 49).any().any():
        errors.append("號碼超出範圍 (1-49)")
    
    # 檢查重複號碼
    for idx, row in data.iterrows():
        if len(set(row)) != len(row):
            errors.append(f"第{idx+1}期有重複號碼")
    
    return errors
```

### 結果評估
```python
def evaluate_prediction(predicted, actual):
    """評估預測結果"""
    hits = len(set(predicted).intersection(set(actual)))
    accuracy = hits / len(predicted)
    return {
        'hits': hits,
        'accuracy': accuracy,
        'predicted': predicted,
        'actual': actual
    }
```

### 視覺化工具
```python
import matplotlib.pyplot as plt
import seaborn as sns

def plot_frequency_distribution(frequency_data):
    """繪製頻率分佈圖"""
    plt.figure(figsize=(12, 6))
    frequency_data.plot(kind='bar')
    plt.title('號碼出現頻率分佈')
    plt.xlabel('號碼')
    plt.ylabel('出現次數')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

def plot_correlation_heatmap(correlation_matrix):
    """繪製相關性熱力圖"""
    plt.figure(figsize=(10, 8))
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
    plt.title('號碼相關性矩陣')
    plt.tight_layout()
    plt.show()
```

## 常用統計檢驗速查

| 檢驗目的 | 方法 | Python函數 | 零假設 |
|---------|------|------------|--------|
| 均勻性 | 卡方檢驗 | `scipy.stats.chisquare` | 分佈均勻 |
| 正態性 | Shapiro-Wilk | `scipy.stats.shapiro` | 正態分佈 |
| 獨立性 | 皮爾森相關 | `scipy.stats.pearsonr` | 無相關 |
| 兩樣本比較 | t檢驗 | `scipy.stats.ttest_ind` | 均值相等 |
| 多樣本比較 | ANOVA | `scipy.stats.f_oneway` | 均值相等 |

## 重要提醒

1. **統計顯著性**：p < 0.05 通常認為統計顯著
2. **樣本大小**：樣本越大，統計結果越可靠
3. **多重檢驗**：進行多次檢驗時需要調整顯著性水準
4. **實際意義**：統計顯著不等於實際意義
5. **隨機性**：彩票本質上是隨機事件，統計分析僅供參考

## 分析流程建議

1. **數據收集** → 獲取完整的歷史開獎數據
2. **數據清理** → 檢查和修正數據錯誤
3. **描述性分析** → 計算基本統計量
4. **分佈分析** → 檢驗數據分佈特徵
5. **假設檢驗** → 驗證統計假設
6. **模式識別** → 尋找潛在規律
7. **預測建模** → 建立預測模型
8. **結果驗證** → 回測和評估模型
9. **持續更新** → 定期更新數據和模型

---

*本參考指南提供快速查找統計方法的便利，詳細理論請參考完整指南文檔。*
