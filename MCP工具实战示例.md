# MCP 工具实战示例

## 目录
1. [Web 应用开发](#web-应用开发)
2. [数据处理管道](#数据处理管道)
3. [系统运维自动化](#系统运维自动化)
4. [API 集成](#api-集成)
5. [文件管理系统](#文件管理系统)

## Web 应用开发

### 场景：创建一个完整的 Web 应用项目

```json
{
  "workflow": "web_app_setup",
  "steps": [
    {
      "step": 1,
      "description": "创建项目目录",
      "tool": "directory_create",
      "parameters": {
        "path": "/projects/my-web-app"
      }
    },
    {
      "step": 2,
      "description": "初始化 Git 仓库",
      "tool": "git_clone",
      "parameters": {
        "repository_url": "https://github.com/template/react-starter.git",
        "destination": "/projects/my-web-app"
      }
    },
    {
      "step": 3,
      "description": "安装依赖",
      "tool": "npm_install",
      "parameters": {
        "package_name": "",
        "project_path": "/projects/my-web-app"
      }
    },
    {
      "step": 4,
      "description": "创建数据库",
      "tool": "db_connect",
      "parameters": {
        "type": "postgresql",
        "connection_string": "postgresql://user:pass@localhost:5432/myapp"
      }
    },
    {
      "step": 5,
      "description": "运行数据库迁移",
      "tool": "db_migrate",
      "parameters": {
        "connection_id": "conn_1",
        "migration_file": "/projects/my-web-app/migrations/001_initial.sql"
      }
    },
    {
      "step": 6,
      "description": "启动开发服务器",
      "tool": "process_start",
      "parameters": {
        "command": "npm start",
        "cwd": "/projects/my-web-app"
      }
    }
  ]
}
```

## 数据处理管道

### 场景：从 API 获取数据并存储到数据库

```json
{
  "workflow": "data_pipeline",
  "steps": [
    {
      "step": 1,
      "description": "从外部 API 获取数据",
      "tool": "http_get",
      "parameters": {
        "url": "https://api.example.com/users",
        "headers": {
          "Authorization": "Bearer your-api-token",
          "Content-Type": "application/json"
        }
      }
    },
    {
      "step": 2,
      "description": "保存原始数据到文件",
      "tool": "file_write",
      "parameters": {
        "path": "/data/raw/users_$(date).json",
        "content": "{{api_response}}"
      }
    },
    {
      "step": 3,
      "description": "连接数据库",
      "tool": "db_connect",
      "parameters": {
        "type": "postgresql",
        "connection_string": "postgresql://user:pass@localhost:5432/datawarehouse"
      }
    },
    {
      "step": 4,
      "description": "清理现有数据",
      "tool": "db_delete",
      "parameters": {
        "connection_id": "conn_1",
        "table": "users_staging",
        "where": {}
      }
    },
    {
      "step": 5,
      "description": "插入新数据",
      "tool": "db_insert",
      "parameters": {
        "connection_id": "conn_1",
        "table": "users_staging",
        "data": "{{processed_users}}"
      }
    },
    {
      "step": 6,
      "description": "运行数据验证",
      "tool": "db_query",
      "parameters": {
        "connection_id": "conn_1",
        "query": "SELECT COUNT(*) FROM users_staging WHERE email IS NULL"
      }
    }
  ]
}
```

## 系统运维自动化

### 场景：服务器健康检查和维护

```json
{
  "workflow": "server_maintenance",
  "steps": [
    {
      "step": 1,
      "description": "检查系统信息",
      "tool": "system_info",
      "parameters": {}
    },
    {
      "step": 2,
      "description": "检查内存使用",
      "tool": "memory_usage",
      "parameters": {}
    },
    {
      "step": 3,
      "description": "检查磁盘使用",
      "tool": "disk_usage",
      "parameters": {
        "path": "/"
      }
    },
    {
      "step": 4,
      "description": "检查关键服务状态",
      "tool": "service_status",
      "parameters": {
        "service_name": "nginx"
      }
    },
    {
      "step": 5,
      "description": "清理日志文件",
      "tool": "file_search",
      "parameters": {
        "pattern": "*.log",
        "directory": "/var/log"
      }
    },
    {
      "step": 6,
      "description": "压缩旧日志",
      "tool": "file_compress",
      "parameters": {
        "source": "/var/log/old_logs",
        "destination": "/backups/logs_$(date).tar.gz",
        "format": "tar.gz"
      }
    },
    {
      "step": 7,
      "description": "创建系统备份",
      "tool": "backup_create",
      "parameters": {
        "source_path": "/etc",
        "backup_path": "/backups/etc_backup_$(date).tar.gz"
      }
    },
    {
      "step": 8,
      "description": "发送状态报告",
      "tool": "http_post",
      "parameters": {
        "url": "https://monitoring.example.com/status",
        "data": {
          "server": "web-01",
          "status": "healthy",
          "timestamp": "{{current_time}}"
        }
      }
    }
  ]
}
```

## API 集成

### 场景：多个 API 数据同步

```json
{
  "workflow": "api_sync",
  "steps": [
    {
      "step": 1,
      "description": "获取 CRM 系统客户数据",
      "tool": "http_get",
      "parameters": {
        "url": "https://crm.example.com/api/customers",
        "headers": {
          "Authorization": "Bearer crm-token"
        }
      }
    },
    {
      "step": 2,
      "description": "获取订单系统数据",
      "tool": "http_get",
      "parameters": {
        "url": "https://orders.example.com/api/orders",
        "headers": {
          "Authorization": "Bearer orders-token"
        }
      }
    },
    {
      "step": 3,
      "description": "合并数据",
      "tool": "file_write",
      "parameters": {
        "path": "/tmp/merged_data.json",
        "content": "{{merged_customer_orders}}"
      }
    },
    {
      "step": 4,
      "description": "上传到数据仓库",
      "tool": "http_post",
      "parameters": {
        "url": "https://warehouse.example.com/api/import",
        "data": "{{merged_data}}",
        "headers": {
          "Content-Type": "application/json",
          "Authorization": "Bearer warehouse-token"
        }
      }
    },
    {
      "step": 5,
      "description": "更新同步状态",
      "tool": "redis_set",
      "parameters": {
        "connection_id": "redis_1",
        "key": "last_sync_timestamp",
        "value": "{{current_timestamp}}",
        "ttl": 86400
      }
    }
  ]
}
```

## 文件管理系统

### 场景：自动化文件整理和备份

```json
{
  "workflow": "file_organization",
  "steps": [
    {
      "step": 1,
      "description": "扫描下载目录",
      "tool": "directory_list",
      "parameters": {
        "path": "/home/<USER>/Downloads",
        "recursive": false
      }
    },
    {
      "step": 2,
      "description": "查找图片文件",
      "tool": "file_search",
      "parameters": {
        "pattern": "*.{jpg,jpeg,png,gif}",
        "directory": "/home/<USER>/Downloads"
      }
    },
    {
      "step": 3,
      "description": "创建图片目录",
      "tool": "directory_create",
      "parameters": {
        "path": "/home/<USER>/Pictures/Downloads"
      }
    },
    {
      "step": 4,
      "description": "移动图片文件",
      "tool": "file_move",
      "parameters": {
        "source": "{{image_files}}",
        "destination": "/home/<USER>/Pictures/Downloads/"
      }
    },
    {
      "step": 5,
      "description": "查找文档文件",
      "tool": "file_search",
      "parameters": {
        "pattern": "*.{pdf,doc,docx,txt}",
        "directory": "/home/<USER>/Downloads"
      }
    },
    {
      "step": 6,
      "description": "移动文档文件",
      "tool": "file_move",
      "parameters": {
        "source": "{{document_files}}",
        "destination": "/home/<USER>/Documents/Downloads/"
      }
    },
    {
      "step": 7,
      "description": "创建备份",
      "tool": "file_backup",
      "parameters": {
        "source": "/home/<USER>/Pictures/Downloads",
        "backup_dir": "/backups/pictures"
      }
    },
    {
      "step": 8,
      "description": "计算文件哈希",
      "tool": "file_hash",
      "parameters": {
        "path": "/home/<USER>/Pictures/Downloads",
        "algorithm": "sha256"
      }
    }
  ]
}
```

## 高级工作流示例

### CI/CD 管道

```json
{
  "workflow": "cicd_pipeline",
  "triggers": ["git_push"],
  "steps": [
    {
      "step": 1,
      "description": "拉取最新代码",
      "tool": "git_pull",
      "parameters": {
        "repository_path": "/app",
        "remote": "origin",
        "branch": "main"
      }
    },
    {
      "step": 2,
      "description": "安装依赖",
      "tool": "npm_install",
      "parameters": {
        "package_name": "",
        "project_path": "/app"
      }
    },
    {
      "step": 3,
      "description": "代码检查",
      "tool": "code_lint",
      "parameters": {
        "file_path": "/app/src",
        "language": "javascript"
      }
    },
    {
      "step": 4,
      "description": "运行测试",
      "tool": "test_run",
      "parameters": {
        "test_path": "/app/tests",
        "framework": "jest"
      }
    },
    {
      "step": 5,
      "description": "构建应用",
      "tool": "build_project",
      "parameters": {
        "project_path": "/app",
        "build_tool": "npm"
      }
    },
    {
      "step": 6,
      "description": "构建 Docker 镜像",
      "tool": "docker_build",
      "parameters": {
        "dockerfile_path": "/app/Dockerfile",
        "image_name": "myapp:latest"
      }
    },
    {
      "step": 7,
      "description": "部署到 Kubernetes",
      "tool": "kubernetes_deploy",
      "parameters": {
        "manifest_path": "/app/k8s/deployment.yaml",
        "namespace": "production"
      }
    },
    {
      "step": 8,
      "description": "健康检查",
      "tool": "http_get",
      "parameters": {
        "url": "https://myapp.example.com/health"
      }
    }
  ]
}
```

## 错误处理和重试机制

```json
{
  "workflow": "robust_data_sync",
  "error_handling": {
    "retry_count": 3,
    "retry_delay": 5000,
    "fallback_actions": ["log_error", "send_alert"]
  },
  "steps": [
    {
      "step": 1,
      "description": "尝试连接主数据库",
      "tool": "db_connect",
      "parameters": {
        "type": "postgresql",
        "connection_string": "postgresql://primary-db:5432/app"
      },
      "on_error": {
        "action": "fallback",
        "fallback_step": "connect_backup_db"
      }
    },
    {
      "step": "connect_backup_db",
      "description": "连接备用数据库",
      "tool": "db_connect",
      "parameters": {
        "type": "postgresql",
        "connection_string": "postgresql://backup-db:5432/app"
      }
    },
    {
      "step": 2,
      "description": "同步数据",
      "tool": "db_query",
      "parameters": {
        "connection_id": "{{active_connection}}",
        "query": "SELECT * FROM users WHERE updated_at > NOW() - INTERVAL '1 hour'"
      }
    }
  ]
}
```

---

*这些实战示例展示了如何在真实场景中组合使用 MCP 工具来完成复杂的任务。每个示例都可以根据具体需求进行调整和扩展。*
