#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BIAS乖離率線條指標
將每天的BIAS值連接起來，形成像移動平均線一樣的連續線條
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from typing import Optional

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class BiasLineIndicator:
    """
    BIAS乖離率線條指標
    將BIAS值連接成連續的線條，像移動平均線一樣顯示
    """
    
    def __init__(self):
        self.data: Optional[pd.DataFrame] = None
        self.bias_periods = [6, 12, 24]  # 多條BIAS線
    
    def generate_sample_data(self, days=240):
        """生成模擬股價數據"""
        start_date = datetime.now() - timedelta(days=days)
        dates = [start_date + timedelta(days=i) for i in range(days)]
        
        np.random.seed(42)
        base_price = 100
        
        # 創建更複雜的價格模式
        trend = np.sin(np.linspace(0, 4*np.pi, days)) * 5  # 週期性波動
        long_trend = np.linspace(0, 15, days)  # 長期上升趨勢
        noise = np.random.normal(0, 1.5, days)  # 隨機波動
        
        prices = base_price + trend + long_trend + noise
        prices = np.maximum(prices, 50)
        
        self.data = pd.DataFrame({
            'date': dates,
            'close': prices
        })
        
        return self.data
    
    def load_data(self, data: pd.DataFrame):
        """載入外部數據"""
        self.data = data.copy()
        return self.data
    
    def calculate_bias_lines(self):
        """計算多條BIAS線"""
        if self.data is None:
            raise ValueError("請先生成或載入數據")
        
        # 計算每個週期的移動平均線和BIAS線
        for period in self.bias_periods:
            # 計算移動平均線
            self.data[f'MA{period}'] = self.data['close'].rolling(window=period).mean()
            
            # 計算BIAS乖離率
            self.data[f'BIAS{period}'] = ((self.data['close'] - self.data[f'MA{period}']) /
                                         self.data[f'MA{period}']) * 100
        
        return self.data
    
    def plot_bias_lines_chart(self):
        """繪製BIAS線條圖表 - 重點展示線條連接效果"""
        if self.data is None:
            self.generate_sample_data()
        
        assert self.data is not None
        # 計算BIAS線
        self.calculate_bias_lines()
        
        # 創建子圖
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10),
                                      gridspec_kw={'height_ratios': [2, 1.5]})
        
        # 上圖：股價和移動平均線
        ax1.plot(self.data['date'], self.data['close'],
                label='股價', linewidth=2.5, color='black', alpha=0.8)
        
        # 繪製移動平均線
        colors = ['red', 'blue', 'green']
        for i, period in enumerate(self.bias_periods):
            ax1.plot(self.data['date'], self.data[f'MA{period}'],
                    label=f'{period}日移動平均線', 
                    linewidth=2, color=colors[i], alpha=0.7)
        
        ax1.set_title('股價走勢與移動平均線', fontsize=16, fontweight='bold')
        ax1.set_ylabel('價格', fontsize=12)
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # 下圖：BIAS線條 - 重點展示連接效果
        for i, period in enumerate(self.bias_periods):
            # 使用粗線條和明顯的顏色來展示連接效果
            ax2.plot(self.data['date'], self.data[f'BIAS{period}'],
                    label=f'BIAS{period}線', 
                    linewidth=3,  # 加粗線條
                    color=colors[i], 
                    alpha=0.8,
                    marker='o',  # 添加點標記
                    markersize=2,  # 小點
                    markerfacecolor=colors[i],
                    markeredgecolor=colors[i])
        
        # 添加零軸線
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.6, linewidth=1.5)
        
        # 添加參考線
        ax2.axhline(y=5, color='red', linestyle='--', alpha=0.5, label='超買參考線(+5%)')
        ax2.axhline(y=-5, color='green', linestyle='--', alpha=0.5, label='超賣參考線(-5%)')
        
        # 填充區域來強調線條效果
        for i, period in enumerate(self.bias_periods):
            ax2.fill_between(self.data['date'], 0, self.data[f'BIAS{period}'],
                           where=(self.data[f'BIAS{period}'] > 0),
                           alpha=0.1, color=colors[i])
            ax2.fill_between(self.data['date'], 0, self.data[f'BIAS{period}'],
                           where=(self.data[f'BIAS{period}'] <= 0),
                           alpha=0.1, color=colors[i])
        
        ax2.set_title('BIAS乖離率線條圖 - 每日連接顯示', fontsize=16, fontweight='bold')
        ax2.set_xlabel('日期', fontsize=12)
        ax2.set_ylabel('BIAS值 (%)', fontsize=12)
        ax2.legend(loc='upper right')
        ax2.grid(True, alpha=0.3)
        
        # 調整日期顯示
        ax1.tick_params(axis='x', rotation=45)
        ax2.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.show()
    
    def plot_single_bias_line(self, period=12):
        """繪製單條BIAS線的詳細圖表"""
        if self.data is None:
            self.generate_sample_data()
        
        assert self.data is not None
        # 確保包含指定週期
        if period not in self.bias_periods:
            self.bias_periods.append(period)
        
        self.calculate_bias_lines()
        
        # 創建圖表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10),
                                      gridspec_kw={'height_ratios': [1.5, 1]})
        
        # 上圖：股價和移動平均線
        ax1.plot(self.data['date'], self.data['close'],
                label='股價', linewidth=2, color='black')
        ax1.plot(self.data['date'], self.data[f'MA{period}'],
                label=f'{period}日移動平均線', linewidth=2, color='blue', alpha=0.7)
        
        ax1.set_title(f'股價與{period}日移動平均線', fontsize=14, fontweight='bold')
        ax1.set_ylabel('價格', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 下圖：BIAS線條 - 強調每日連接
        ax2.plot(self.data['date'], self.data[f'BIAS{period}'],
                           label=f'BIAS{period}線條', 
                           linewidth=4,  # 很粗的線條
                           color='red', 
                           alpha=0.8)
        
        # 添加數據點
        ax2.scatter(self.data['date'], self.data[f'BIAS{period}'],
                   s=15, color='red', alpha=0.6, zorder=5)
        
        # 零軸線
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.6, linewidth=2)
        
        # 參考線
        ax2.axhline(y=10, color='orange', linestyle='--', alpha=0.7, label='超買線(+10%)')
        ax2.axhline(y=-10, color='blue', linestyle='--', alpha=0.7, label='超賣線(-10%)')
        
        # 填充正負區域
        ax2.fill_between(self.data['date'], 0, self.data[f'BIAS{period}'],
                        where=(self.data[f'BIAS{period}'] > 0),
                        alpha=0.2, color='red', label='正乖離區域')
        ax2.fill_between(self.data['date'], 0, self.data[f'BIAS{period}'],
                        where=(self.data[f'BIAS{period}'] <= 0),
                        alpha=0.2, color='blue', label='負乖離區域')
        
        ax2.set_title(f'BIAS{period}乖離率線條 - 每日數值連接', fontsize=14, fontweight='bold')
        ax2.set_xlabel('日期', fontsize=12)
        ax2.set_ylabel('BIAS值 (%)', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def print_bias_analysis(self):
        """打印BIAS線條分析"""
        if self.data is None:
            self.generate_sample_data()
        
        assert self.data is not None
        self.calculate_bias_lines()
        
        latest_data = self.data.iloc[-1]
        
        print("=" * 60)
        print("BIAS乖離率線條分析報告")
        print("=" * 60)
        print(f"分析日期: {latest_data['date'].strftime('%Y-%m-%d')}")
        print(f"當前股價: {latest_data['close']:.2f}")
        print("-" * 60)
        
        for period in self.bias_periods:
            bias_value = latest_data[f'BIAS{period}']
            ma_value = latest_data[f'MA{period}']
            
            # 判斷狀態
            if bias_value > 5:
                status = "超買"
            elif bias_value < -5:
                status = "超賣"
            elif bias_value > 0:
                status = "正乖離"
            else:
                status = "負乖離"
            
            print(f"BIAS{period}線: {bias_value:+6.2f}% (MA{period}: {ma_value:6.2f}) - {status}")
        
        print("-" * 60)
        
        # 分析線條趨勢
        print("線條趨勢分析:")
        for period in self.bias_periods:
            recent_bias = self.data[f'BIAS{period}'].tail(5)
            if len(recent_bias) >= 2:
                trend = "上升" if recent_bias.iloc[-1] > recent_bias.iloc[-2] else "下降"
                print(f"  BIAS{period}線: {trend}趨勢")
        
        print("=" * 60)
    
    def get_bias_line_data(self):
        """獲取BIAS線條數據"""
        if self.data is None:
            self.generate_sample_data()

        assert self.data is not None
        if 'BIAS12' not in self.data.columns:
            self.calculate_bias_lines()
        
        result = {}
        for period in self.bias_periods:
            result[f'BIAS{period}'] = self.data[f'BIAS{period}'].tolist()
        
        result['dates'] = self.data['date'].tolist()
        result['prices'] = self.data['close'].tolist()
        
        return result

def main():
    """主函數 - 展示BIAS線條效果"""
    print("📈 BIAS乖離率線條指標演示")
    print("=" * 40)
    
    # 創建指標實例
    bias_line = BiasLineIndicator()
    
    # 生成數據
    print("生成模擬數據...")
    bias_line.generate_sample_data(240)
    
    # 打印分析
    bias_line.print_bias_analysis()
    
    # 繪製多條BIAS線
    print("\n繪製多條BIAS線圖表...")
    bias_line.plot_bias_lines_chart()
    
    # 繪製單條BIAS線詳細圖
    print("\n繪製單條BIAS線詳細圖...")
    bias_line.plot_single_bias_line(period=12)
    
    print("\n✅ BIAS線條演示完成！")
    print("💡 每個BIAS值都被連接成連續的線條，就像移動平均線一樣！")

if __name__ == "__main__":
    main()
