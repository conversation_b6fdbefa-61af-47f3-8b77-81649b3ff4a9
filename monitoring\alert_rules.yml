# Prometheus告警規則
groups:
  - name: lottery_analyzer_alerts
    rules:
      # 服務可用性告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服務 {{ $labels.job }} 已停止"
          description: "服務 {{ $labels.job }} 在實例 {{ $labels.instance }} 上已停止超過1分鐘"

      # 高CPU使用率告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "高CPU使用率"
          description: "實例 {{ $labels.instance }} 的CPU使用率超過80%，當前值: {{ $value }}%"

      # 高內存使用率告警
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "高內存使用率"
          description: "實例 {{ $labels.instance }} 的內存使用率超過85%，當前值: {{ $value }}%"

      # 磁盤空間不足告警
      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "磁盤空間不足"
          description: "實例 {{ $labels.instance }} 的磁盤 {{ $labels.mountpoint }} 使用率超過90%，當前值: {{ $value }}%"

      # 數據庫連接告警
      - alert: DatabaseConnectionFailed
        expr: pg_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "數據庫連接失敗"
          description: "PostgreSQL數據庫連接失敗，實例: {{ $labels.instance }}"

      # Redis連接告警
      - alert: RedisConnectionFailed
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis連接失敗"
          description: "Redis連接失敗，實例: {{ $labels.instance }}"

      # 應用程序錯誤率告警
      - alert: HighErrorRate
        expr: rate(lottery_analyzer_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "應用程序錯誤率過高"
          description: "大樂透分析系統錯誤率超過10%，當前值: {{ $value }}"

      # 分析任務失敗告警
      - alert: AnalysisTaskFailed
        expr: increase(lottery_analysis_failed_total[5m]) > 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "分析任務失敗"
          description: "大樂透分析任務失敗，失敗次數: {{ $value }}"

      # 響應時間過長告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(lottery_analyzer_request_duration_seconds_bucket[5m])) > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "響應時間過長"
          description: "95%的請求響應時間超過5秒，當前值: {{ $value }}秒"

      # 容器重啟告警
      - alert: ContainerRestarted
        expr: increase(container_start_time_seconds[5m]) > 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "容器重啟"
          description: "容器 {{ $labels.name }} 在過去5分鐘內重啟了 {{ $value }} 次"

  - name: system_alerts
    rules:
      # 系統負載告警
      - alert: HighSystemLoad
        expr: node_load1 > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "系統負載過高"
          description: "實例 {{ $labels.instance }} 的1分鐘負載超過2，當前值: {{ $value }}"

      # 網絡連接數告警
      - alert: TooManyConnections
        expr: node_netstat_Tcp_CurrEstab > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "TCP連接數過多"
          description: "實例 {{ $labels.instance }} 的TCP連接數超過1000，當前值: {{ $value }}"

      # 文件描述符使用率告警
      - alert: HighFileDescriptorUsage
        expr: (node_filefd_allocated / node_filefd_maximum) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "文件描述符使用率過高"
          description: "實例 {{ $labels.instance }} 的文件描述符使用率超過80%，當前值: {{ $value }}%"

  - name: application_alerts
    rules:
      # 數據處理延遲告警
      - alert: DataProcessingDelay
        expr: lottery_data_processing_lag_seconds > 300
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "數據處理延遲"
          description: "數據處理延遲超過5分鐘，當前延遲: {{ $value }}秒"

      # 預測準確率下降告警
      - alert: PredictionAccuracyDrop
        expr: lottery_prediction_accuracy < 0.6
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "預測準確率下降"
          description: "預測準確率低於60%，當前值: {{ $value }}"

      # 數據質量問題告警
      - alert: DataQualityIssue
        expr: lottery_data_quality_score < 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "數據質量問題"
          description: "數據質量評分低於0.8，當前值: {{ $value }}"
