name: 大樂透分析系統 CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: lottery-analyzer

jobs:
  # 代碼質量檢查
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - name: 檢出代碼
      uses: actions/checkout@v4
      
    - name: 設置Python環境
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'
        
    - name: 安裝依賴
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy
        pip install -r requirements.txt
        
    - name: 代碼格式檢查
      run: |
        black --check .
        isort --check-only .
        
    - name: 代碼風格檢查
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
        
    - name: 類型檢查
      run: |
        mypy . --ignore-missing-imports

  # 安全掃描
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - name: 檢出代碼
      uses: actions/checkout@v4
      
    - name: 運行安全掃描
      uses: github/super-linter@v4
      env:
        DEFAULT_BRANCH: main
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        VALIDATE_PYTHON_BANDIT: true
        VALIDATE_DOCKERFILE_HADOLINT: true
        
    - name: 依賴安全檢查
      run: |
        pip install safety
        safety check -r requirements.txt

  # 單元測試
  test:
    runs-on: ubuntu-latest
    needs: [code-quality]
    
    strategy:
      matrix:
        python-version: [3.8, 3.9, '3.10']
        
    steps:
    - name: 檢出代碼
      uses: actions/checkout@v4
      
    - name: 設置Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: 安裝系統依賴
      run: |
        sudo apt-get update
        sudo apt-get install -y python3-tk fonts-wqy-zenhei
        
    - name: 安裝Python依賴
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov pytest-xdist
        pip install -r requirements.txt
        
    - name: 運行測試
      run: |
        export MPLBACKEND=Agg
        pytest tests/ -v --cov=./ --cov-report=xml --cov-report=html -n auto
        
    - name: 上傳覆蓋率報告
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        
    - name: 保存測試結果
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.python-version }}
        path: |
          htmlcov/
          coverage.xml

  # 構建Docker鏡像
  build:
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      
    steps:
    - name: 檢出代碼
      uses: actions/checkout@v4
      
    - name: 設置Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: 登錄容器註冊表
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: 提取元數據
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
          
    - name: 構建並推送鏡像
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 集成測試
  integration-test:
    runs-on: ubuntu-latest
    needs: [build]
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_lottery
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          
    steps:
    - name: 檢出代碼
      uses: actions/checkout@v4
      
    - name: 運行集成測試
      run: |
        docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit
        
    - name: 收集測試日誌
      if: always()
      run: |
        docker-compose -f docker-compose.test.yml logs > integration-test.log
        
    - name: 上傳測試日誌
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-logs
        path: integration-test.log

  # 性能測試
  performance-test:
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: 檢出代碼
      uses: actions/checkout@v4
      
    - name: 運行性能測試
      run: |
        docker run --rm \
          -v $(pwd)/tests/performance:/tests \
          ${{ env.REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}:main \
          python -m pytest /tests -v --benchmark-only
          
    - name: 上傳性能報告
      uses: actions/upload-artifact@v3
      with:
        name: performance-report
        path: benchmark-results/

  # 部署到測試環境
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [integration-test]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: 檢出代碼
      uses: actions/checkout@v4
      
    - name: 部署到測試環境
      run: |
        echo "部署到測試環境..."
        # 這裡添加實際的部署腳本
        
    - name: 運行煙霧測試
      run: |
        echo "運行煙霧測試..."
        # 這裡添加煙霧測試腳本

  # 部署到生產環境
  deploy-production:
    runs-on: ubuntu-latest
    needs: [performance-test]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: 檢出代碼
      uses: actions/checkout@v4
      
    - name: 部署到生產環境
      run: |
        echo "部署到生產環境..."
        # 這裡添加實際的部署腳本
        
    - name: 健康檢查
      run: |
        echo "執行健康檢查..."
        # 這裡添加健康檢查腳本
        
    - name: 發送部署通知
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  # 發布Release
  release:
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: github.event_name == 'release'
    
    steps:
    - name: 檢出代碼
      uses: actions/checkout@v4
      
    - name: 創建Release包
      run: |
        tar -czf lottery-analyzer-${{ github.event.release.tag_name }}.tar.gz \
          --exclude='.git' \
          --exclude='tests' \
          --exclude='__pycache__' \
          .
          
    - name: 上傳Release資產
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: ./lottery-analyzer-${{ github.event.release.tag_name }}.tar.gz
        asset_name: lottery-analyzer-${{ github.event.release.tag_name }}.tar.gz
        asset_content_type: application/gzip

  # 清理
  cleanup:
    runs-on: ubuntu-latest
    needs: [deploy-production, release]
    if: always()
    
    steps:
    - name: 清理舊鏡像
      run: |
        echo "清理舊的容器鏡像..."
        # 這裡添加清理腳本
