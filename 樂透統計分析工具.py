#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
樂透統計分析工具
採用一般統計方法進行系統性分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency, shapiro, normaltest, chisquare
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class LotteryStatisticalAnalyzer:
    """樂透統計分析器 - 採用一般統計方法"""
    
    def __init__(self):
        self.data = None
        self.analysis_results = {}
        
    def load_data(self, data_source):
        """載入樂透數據"""
        if isinstance(data_source, str):
            self.data = pd.read_csv(data_source)
        else:
            self.data = data_source.copy()
        
        self._preprocess_data()
        print(f"✅ 已載入 {len(self.data)} 期樂透數據")
        
    def _preprocess_data(self):
        """數據預處理"""
        # 確保數據格式正確
        if 'numbers' not in self.data.columns:
            # 假設前6列是主號碼
            number_cols = [col for col in self.data.columns if any(x in col.lower() for x in ['number', '號碼'])]
            if len(number_cols) >= 6:
                self.data['numbers'] = self.data[number_cols[:6]].values.tolist()
            else:
                self.data['numbers'] = self.data.iloc[:, :6].values.tolist()
        
        # 計算衍生變數
        self.data['sum_value'] = self.data['numbers'].apply(sum)
        self.data['odd_count'] = self.data['numbers'].apply(lambda x: sum(1 for n in x if n % 2 == 1))
        self.data['big_count'] = self.data['numbers'].apply(lambda x: sum(1 for n in x if n > 25))
        
    def descriptive_statistics(self):
        """描述性統計分析"""
        print("\n" + "="*50)
        print("📊 描述性統計分析")
        print("="*50)
        
        results = {}
        
        # 1. 號碼頻率統計
        all_numbers = [num for numbers in self.data['numbers'] for num in numbers]
        frequency_counter = Counter(all_numbers)
        
        # 計算統計指標
        frequencies = list(frequency_counter.values())
        total_periods = len(self.data)
        expected_freq = total_periods * 6 / 49
        
        freq_stats = {
            'mean': np.mean(frequencies),
            'std': np.std(frequencies),
            'cv': np.std(frequencies) / np.mean(frequencies),
            'min': min(frequencies),
            'max': max(frequencies),
            'expected': expected_freq
        }
        
        results['frequency_stats'] = freq_stats
        
        print(f"號碼頻率統計:")
        print(f"  平均出現次數: {freq_stats['mean']:.2f}")
        print(f"  標準差: {freq_stats['std']:.2f}")
        print(f"  變異係數: {freq_stats['cv']:.4f}")
        print(f"  理論期望值: {freq_stats['expected']:.2f}")
        
        # 2. 和值統計
        sum_stats = {
            'mean': self.data['sum_value'].mean(),
            'std': self.data['sum_value'].std(),
            'min': self.data['sum_value'].min(),
            'max': self.data['sum_value'].max(),
            'median': self.data['sum_value'].median(),
            'skewness': stats.skew(self.data['sum_value']),
            'kurtosis': stats.kurtosis(self.data['sum_value'])
        }
        
        results['sum_stats'] = sum_stats
        
        print(f"\n和值統計:")
        print(f"  平均值: {sum_stats['mean']:.2f}")
        print(f"  標準差: {sum_stats['std']:.2f}")
        print(f"  偏度: {sum_stats['skewness']:.4f}")
        print(f"  峰度: {sum_stats['kurtosis']:.4f}")
        
        # 3. 奇偶分佈統計
        odd_distribution = self.data['odd_count'].value_counts().sort_index()
        results['odd_distribution'] = odd_distribution
        
        print(f"\n奇偶分佈:")
        for odd_count, freq in odd_distribution.items():
            percentage = freq / total_periods * 100
            print(f"  {odd_count}奇{6-odd_count}偶: {freq}次 ({percentage:.1f}%)")
        
        self.analysis_results['descriptive'] = results
        return results
    
    def probability_distribution_tests(self):
        """機率分佈檢驗"""
        print("\n" + "="*50)
        print("🎲 機率分佈檢驗")
        print("="*50)
        
        results = {}
        
        # 1. 均勻分佈檢驗（號碼頻率）
        all_numbers = [num for numbers in self.data['numbers'] for num in numbers]
        observed_freq = [all_numbers.count(i) for i in range(1, 50)]
        expected_freq = len(all_numbers) / 49
        
        chi2_stat, p_value = chisquare(observed_freq)
        
        results['uniformity_test'] = {
            'chi2_statistic': chi2_stat,
            'p_value': p_value,
            'is_uniform': p_value > 0.05
        }
        
        print(f"號碼均勻分佈檢驗:")
        print(f"  卡方統計量: {chi2_stat:.4f}")
        print(f"  p值: {p_value:.6f}")
        print(f"  結論: {'符合均勻分佈' if p_value > 0.05 else '不符合均勻分佈'}")
        
        # 2. 正態分佈檢驗（和值）
        shapiro_stat, shapiro_p = shapiro(self.data['sum_value'])
        
        results['normality_test'] = {
            'shapiro_statistic': shapiro_stat,
            'p_value': shapiro_p,
            'is_normal': shapiro_p > 0.05
        }
        
        print(f"\n和值正態分佈檢驗:")
        print(f"  Shapiro-Wilk統計量: {shapiro_stat:.4f}")
        print(f"  p值: {shapiro_p:.6f}")
        print(f"  結論: {'符合正態分佈' if shapiro_p > 0.05 else '不符合正態分佈'}")
        
        # 3. 獨立性檢驗
        if len(self.data) > 1:
            current_sums = self.data['sum_value'][1:].values
            previous_sums = self.data['sum_value'][:-1].values
            
            correlation, corr_p = stats.pearsonr(current_sums, previous_sums)
            
            results['independence_test'] = {
                'correlation': correlation,
                'p_value': corr_p,
                'is_independent': abs(correlation) < 0.1 and corr_p > 0.05
            }
            
            print(f"\n期間獨立性檢驗:")
            print(f"  相關係數: {correlation:.6f}")
            print(f"  p值: {corr_p:.6f}")
            print(f"  結論: {'期間獨立' if abs(correlation) < 0.1 else '存在相關性'}")
        
        self.analysis_results['distribution_tests'] = results
        return results
    
    def frequency_analysis(self):
        """頻率分析"""
        print("\n" + "="*50)
        print("📈 頻率分析")
        print("="*50)
        
        # 計算每個號碼的出現頻率
        all_numbers = [num for numbers in self.data['numbers'] for num in numbers]
        frequency_counter = Counter(all_numbers)
        
        # 轉換為DataFrame便於分析
        freq_df = pd.DataFrame(list(frequency_counter.items()), 
                              columns=['number', 'frequency'])
        freq_df = freq_df.sort_values('number')
        
        # 計算統計指標
        mean_freq = freq_df['frequency'].mean()
        std_freq = freq_df['frequency'].std()
        
        # 分類號碼
        hot_threshold = mean_freq + std_freq
        cold_threshold = mean_freq - std_freq
        
        hot_numbers = freq_df[freq_df['frequency'] > hot_threshold]['number'].tolist()
        cold_numbers = freq_df[freq_df['frequency'] < cold_threshold]['number'].tolist()
        normal_numbers = freq_df[
            (freq_df['frequency'] >= cold_threshold) & 
            (freq_df['frequency'] <= hot_threshold)
        ]['number'].tolist()
        
        results = {
            'frequency_data': freq_df,
            'mean_frequency': mean_freq,
            'std_frequency': std_freq,
            'hot_numbers': hot_numbers,
            'cold_numbers': cold_numbers,
            'normal_numbers': normal_numbers
        }
        
        print(f"頻率分析結果:")
        print(f"  平均頻率: {mean_freq:.2f}")
        print(f"  標準差: {std_freq:.2f}")
        print(f"  熱門號碼 (>{hot_threshold:.1f}次): {hot_numbers}")
        print(f"  冷門號碼 (<{cold_threshold:.1f}次): {cold_numbers}")
        print(f"  一般號碼: {len(normal_numbers)}個")
        
        self.analysis_results['frequency'] = results
        return results
    
    def interval_analysis(self):
        """間隔分析"""
        print("\n" + "="*50)
        print("⏱️ 間隔分析")
        print("="*50)
        
        results = {}
        
        for number in range(1, 50):
            # 找出該號碼出現的期數
            appearances = []
            for idx, numbers in enumerate(self.data['numbers']):
                if number in numbers:
                    appearances.append(idx)
            
            if len(appearances) > 1:
                # 計算間隔
                intervals = [appearances[i+1] - appearances[i] 
                           for i in range(len(appearances)-1)]
                
                results[number] = {
                    'appearances': appearances,
                    'intervals': intervals,
                    'avg_interval': np.mean(intervals),
                    'std_interval': np.std(intervals),
                    'last_appearance': appearances[-1] if appearances else None,
                    'current_interval': len(self.data) - 1 - appearances[-1] if appearances else len(self.data)
                }
        
        # 統計摘要
        all_intervals = [interval for data in results.values() 
                        for interval in data['intervals']]
        
        interval_summary = {
            'mean_interval': np.mean(all_intervals),
            'std_interval': np.std(all_intervals),
            'min_interval': min(all_intervals),
            'max_interval': max(all_intervals)
        }
        
        print(f"間隔統計摘要:")
        print(f"  平均間隔: {interval_summary['mean_interval']:.2f}期")
        print(f"  標準差: {interval_summary['std_interval']:.2f}期")
        print(f"  最短間隔: {interval_summary['min_interval']}期")
        print(f"  最長間隔: {interval_summary['max_interval']}期")
        
        # 找出當前間隔較長的號碼
        long_interval_numbers = []
        for number, data in results.items():
            if data['current_interval'] > data['avg_interval'] + data['std_interval']:
                long_interval_numbers.append((number, data['current_interval']))
        
        long_interval_numbers.sort(key=lambda x: x[1], reverse=True)
        
        print(f"\n當前間隔較長的號碼:")
        for number, interval in long_interval_numbers[:10]:
            print(f"  號碼{number}: 已{interval}期未出現")
        
        results['summary'] = interval_summary
        results['long_interval'] = long_interval_numbers
        
        self.analysis_results['interval'] = results
        return results
    
    def pattern_analysis(self):
        """模式分析"""
        print("\n" + "="*50)
        print("🎨 模式分析")
        print("="*50)
        
        results = {}
        
        # 1. 連號分析
        consecutive_counts = []
        for numbers in self.data['numbers']:
            sorted_numbers = sorted(numbers)
            consecutive = 0
            max_consecutive = 0
            
            for i in range(1, len(sorted_numbers)):
                if sorted_numbers[i] == sorted_numbers[i-1] + 1:
                    consecutive += 1
                    max_consecutive = max(max_consecutive, consecutive + 1)
                else:
                    consecutive = 0
            
            consecutive_counts.append(max_consecutive)
        
        consecutive_freq = Counter(consecutive_counts)
        results['consecutive_analysis'] = consecutive_freq
        
        print(f"連號分析:")
        for count, freq in sorted(consecutive_freq.items()):
            percentage = freq / len(self.data) * 100
            print(f"  {count}個連號: {freq}次 ({percentage:.1f}%)")
        
        # 2. 重複號分析
        repeat_counts = []
        for i in range(1, len(self.data)):
            current_set = set(self.data['numbers'].iloc[i])
            previous_set = set(self.data['numbers'].iloc[i-1])
            repeat_count = len(current_set.intersection(previous_set))
            repeat_counts.append(repeat_count)
        
        repeat_freq = Counter(repeat_counts)
        results['repeat_analysis'] = repeat_freq
        
        print(f"\n重複號分析:")
        for count, freq in sorted(repeat_freq.items()):
            percentage = freq / len(repeat_counts) * 100
            print(f"  {count}個重複號: {freq}次 ({percentage:.1f}%)")
        
        # 3. 區間分佈分析
        zone_analysis = []
        for numbers in self.data['numbers']:
            zones = [0] * 7  # 7個區間 (1-7, 8-14, ..., 43-49)
            for num in numbers:
                zone_idx = min((num - 1) // 7, 6)
                zones[zone_idx] += 1
            zone_analysis.append(zones)
        
        zone_df = pd.DataFrame(zone_analysis, 
                              columns=[f'區間{i+1}' for i in range(7)])
        zone_means = zone_df.mean()
        
        results['zone_analysis'] = zone_means
        
        print(f"\n區間分佈分析:")
        for i, mean_count in enumerate(zone_means):
            zone_range = f"{i*7+1}-{min((i+1)*7, 49)}"
            print(f"  區間{i+1} ({zone_range}): 平均{mean_count:.2f}個號碼")
        
        self.analysis_results['pattern'] = results
        return results
    
    def comprehensive_analysis(self):
        """綜合統計分析"""
        print("🎯 開始樂透綜合統計分析")
        print("="*60)
        
        # 執行所有分析
        desc_results = self.descriptive_statistics()
        dist_results = self.probability_distribution_tests()
        freq_results = self.frequency_analysis()
        interval_results = self.interval_analysis()
        pattern_results = self.pattern_analysis()
        
        # 生成綜合報告
        self._generate_comprehensive_report()
        
        return self.analysis_results
    
    def _generate_comprehensive_report(self):
        """生成綜合分析報告"""
        print("\n" + "="*60)
        print("📋 綜合分析報告")
        print("="*60)
        
        print(f"\n【數據概況】")
        print(f"分析期數: {len(self.data)} 期")
        print(f"總號碼數: {len(self.data) * 6} 個")
        
        # 隨機性評估
        print(f"\n【隨機性評估】")
        if 'distribution_tests' in self.analysis_results:
            tests = self.analysis_results['distribution_tests']
            
            uniformity = "✅ 通過" if tests['uniformity_test']['is_uniform'] else "❌ 未通過"
            print(f"均勻分佈檢驗: {uniformity}")
            
            if 'independence_test' in tests:
                independence = "✅ 通過" if tests['independence_test']['is_independent'] else "❌ 未通過"
                print(f"獨立性檢驗: {independence}")
        
        # 統計特徵摘要
        print(f"\n【統計特徵摘要】")
        if 'descriptive' in self.analysis_results:
            desc = self.analysis_results['descriptive']
            cv = desc['frequency_stats']['cv']
            
            if cv < 0.1:
                print(f"頻率變異係數: {cv:.4f} (分佈均勻)")
            elif cv < 0.2:
                print(f"頻率變異係數: {cv:.4f} (輕微不均)")
            else:
                print(f"頻率變異係數: {cv:.4f} (分佈不均)")
        
        print(f"\n【結論】")
        print("基於統計分析結果:")
        print("1. 樂透開獎具有良好的隨機性特徵")
        print("2. 號碼出現頻率符合理論期望")
        print("3. 各期開獎結果相互獨立")
        print("4. 統計分析無法提高中獎機率")
        print("\n⚠️  重要提醒: 請理性參與，量力而為！")

def create_sample_data(periods=200):
    """創建示例樂透數據"""
    np.random.seed(42)
    data = []
    
    for i in range(periods):
        # 隨機生成6個不重複的號碼
        numbers = sorted(np.random.choice(range(1, 50), size=6, replace=False))
        
        period_data = {
            'period': f'第{i+1:03d}期',
            'numbers': numbers,
            'special': np.random.choice([n for n in range(1, 50) if n not in numbers])
        }
        data.append(period_data)
    
    return pd.DataFrame(data)

if __name__ == "__main__":
    # 示例使用
    print("🎲 樂透統計分析工具")
    print("採用一般統計方法進行系統性分析")
    print("="*50)
    
    # 創建示例數據
    sample_data = create_sample_data(300)
    
    # 初始化分析器
    analyzer = LotteryStatisticalAnalyzer()
    analyzer.load_data(sample_data)
    
    # 執行綜合分析
    results = analyzer.comprehensive_analysis()
    
    print(f"\n📊 分析完成！")
    print("可以使用以下方法查看詳細結果:")
    print("- analyzer.analysis_results['descriptive']")
    print("- analyzer.analysis_results['distribution_tests']")
    print("- analyzer.analysis_results['frequency']")
    print("- analyzer.analysis_results['interval']")
    print("- analyzer.analysis_results['pattern']")
