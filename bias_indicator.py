import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class MovingAverageIndicator:
    def __init__(self):
        self.data = None
    
    def generate_sample_data(self, days=60):
        """生成模拟股价数据"""
        # 创建日期序列
        start_date = datetime.now() - timedelta(days=days)
        dates = [start_date + timedelta(days=i) for i in range(days)]
        
        # 生成模拟价格数据（带有趋势和随机波动）
        np.random.seed(42)  # 固定随机种子以便重现
        base_price = 100
        trend = np.linspace(0, 10, days)  # 上升趋势
        noise = np.random.normal(0, 2, days)  # 随机波动
        prices = base_price + trend + noise
        
        # 确保价格为正数
        prices = np.maximum(prices, 50)
        
        self.data = pd.DataFrame({
            'date': dates,
            'close': prices
        })
        
        return self.data
    
    def calculate_moving_average(self, period=20):
        """计算移动平均线"""
        if self.data is None:
            raise ValueError("请先生成或加载数据")

        self.data[f'MA{period}'] = self.data['close'].rolling(window=period).mean()
        return self.data[f'MA{period}']

    def calculate_bias(self, period=20):
        """计算BIAS指标"""
        if self.data is None:
            raise ValueError("请先生成或加载数据")

        if f'MA{period}' not in self.data.columns:
            self.calculate_moving_average(period)

        # BIAS = (收盘价 - 移动平均线) / 移动平均线 * 100
        self.data[f'BIAS{period}'] = ((self.data['close'] - self.data[f'MA{period}']) /
                                      self.data[f'MA{period}']) * 100

        return self.data[f'BIAS{period}']
    

    
    def plot_bias_chart(self, period=20, period2=60):
        """绘制BIAS指标图表"""
        if self.data is None:
            self.generate_sample_data()

        # 确保数据已正确加载
        if self.data is None:
            raise ValueError("无法生成或加载数据")

        # 计算移动平均线和BIAS指标
        self.calculate_bias(period)
        self.calculate_bias(period2)

        # 创建子图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10),
                                       gridspec_kw={'height_ratios': [2, 1]})

        # 上图：股价和移动平均线
        ax1.plot(self.data['date'], self.data['close'],
                label='股价', linewidth=2, color='blue')
        ax1.plot(self.data['date'], self.data[f'MA{period}'],
                label=f'{period}日移动平均线', linewidth=2, color='red', alpha=0.7)
        ax1.plot(self.data['date'], self.data[f'MA{period2}'],
                label=f'{period2}日移动平均线', linewidth=2, color='orange', alpha=0.7)
        
        ax1.set_title(f'股价走势与{period}日、{period2}日移动平均线', fontsize=14, fontweight='bold')
        ax1.set_ylabel('价格', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 下图：BIAS指标对比
        ax2.plot(self.data['date'], self.data[f'BIAS{period}'],
                label=f'BIAS{period}', linewidth=2, color='green')
        ax2.plot(self.data['date'], self.data[f'BIAS{period2}'],
                label=f'BIAS{period2}', linewidth=2, color='purple', alpha=0.8)

        # 添加超买超卖线
        ax2.axhline(y=10, color='red', linestyle='--', alpha=0.7, label='超买线(+10%)')
        ax2.axhline(y=-10, color='blue', linestyle='--', alpha=0.7, label='超卖线(-10%)')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5, label='零轴')

        # 填充超买超卖区域
        ax2.fill_between(self.data['date'], 10, 20, alpha=0.2, color='red', label='超买区域')
        ax2.fill_between(self.data['date'], -10, -20, alpha=0.2, color='blue', label='超卖区域')

        ax2.set_title(f'BIAS{period}与BIAS{period2}指标对比', fontsize=14, fontweight='bold')
        ax2.set_xlabel('日期', fontsize=12)
        ax2.set_ylabel('BIAS值 (%)', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def get_trading_signals(self, period=20):
        """获取交易信号"""
        if self.data is None:
            raise ValueError("请先生成或加载数据")

        if f'BIAS{period}' not in self.data.columns:
            self.calculate_bias(period)

        signals = []
        bias_values = self.data[f'BIAS{period}'].dropna()

        for date, bias in zip(self.data['date'][period-1:], bias_values):
            if bias > 10:
                signals.append((date, '卖出信号', f'BIAS{period}={bias:.2f}% (超买)'))
            elif bias < -10:
                signals.append((date, '买入信号', f'BIAS{period}={bias:.2f}% (超卖)'))

        return signals
    
    def print_analysis(self, period=20):
        """打印分析结果"""
        if self.data is None:
            self.generate_sample_data()

        # 确保数据已正确加载
        if self.data is None:
            raise ValueError("无法生成或加载数据")

        # 计算BIAS指标
        self.calculate_bias(period)

        latest_data = self.data.iloc[-1]

        print("=" * 50)
        print("BIAS乖離率分析报告")
        print("=" * 50)
        print(f"分析日期: {latest_data['date'].strftime('%Y-%m-%d')}")
        print(f"当前股价: {latest_data['close']:.2f}")
        print(f"{period}日移动平均线: {latest_data[f'MA{period}']:.2f}")
        print(f"BIAS{period}值: {latest_data[f'BIAS{period}']:.2f}%")

        # 判断当前状态
        bias_value = latest_data[f'BIAS{period}']
        if bias_value > 10:
            status = "超买状态，可能面临回调压力"
        elif bias_value < -10:
            status = "超卖状态，可能出现反弹机会"
        elif bias_value > 5:
            status = "偏高状态，注意风险"
        elif bias_value < -5:
            status = "偏低状态，可关注机会"
        else:
            status = "正常区间，继续观察趋势"

        print(f"当前状态: {status}")
        
        # 显示交易信号
        signals = self.get_trading_signals(period)
        if signals:
            print(f"\n最近的交易信号:")
            for signal in signals[-5:]:  # 显示最近5个信号
                print(f"  {signal[0].strftime('%Y-%m-%d')}: {signal[1]} - {signal[2]}")
        
        print("=" * 50)

    def compare_bias_periods(self, periods=[6, 20, 60]):
        """比较不同周期的BIAS指标"""
        if self.data is None:
            self.generate_sample_data()

        # 确保数据已正确加载
        if self.data is None:
            raise ValueError("无法生成或加载数据")

        print("=" * 60)
        print("多周期BIAS乖離率对比分析")
        print("=" * 60)

        current_price = self.data['close'].iloc[-1]
        print(f"当前股价: {current_price:.2f}")
        print("-" * 60)

        for period in periods:
            self.calculate_bias(period)
            latest_bias = self.data[f'BIAS{period}'].iloc[-1]
            latest_ma = self.data[f'MA{period}'].iloc[-1]

            # 判断状态
            if latest_bias > 10:
                status = "超买"
            elif latest_bias < -10:
                status = "超卖"
            elif latest_bias > 5:
                status = "偏高"
            elif latest_bias < -5:
                status = "偏低"
            else:
                status = "正常"

            print(f"BIAS{period:2d}: {latest_bias:+6.2f}% (MA{period}: {latest_ma:6.2f}) - {status}")

        print("=" * 60)

# 使用示例
if __name__ == "__main__":
    # 创建BIAS指标实例
    bias_indicator = MovingAverageIndicator()

    # 生成示例数据
    print("正在生成模拟股价数据...")
    bias_indicator.generate_sample_data(days=60)

    # 打印分析报告
    bias_indicator.print_analysis(period=20)

    # 绘制图表
    print("\n正在生成BIAS指标图表...")
    bias_indicator.plot_bias_chart(period=20, period2=60)

    # 使用新的比较方法
    print("\n")
    bias_indicator.compare_bias_periods([6, 20, 60])
