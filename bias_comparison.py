import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 解決 matplotlib 中文顯示問題
plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei'] # 指定默認字形
plt.rcParams['axes.unicode_minus'] = False # 解決保存圖像是負號'-'顯示為方塊的問題

# 1. 準備範例數據
# 假設這是一段時間的每日收盤價
data = {
    'price': [
        100, 102, 105, 103, 108, 110, 112, 109, 106, 104,
        107, 111, 115, 118, 122, 119, 116, 113, 110, 114
    ]
}
df = pd.DataFrame(data)

# 2. 計算移動平均線 (MA)
# 我們使用 6 日簡單移動平均線 (SMA)
n = 6
df['MA'] = df['price'].rolling(window=n).mean()

# 3. 計算兩種 BIAS
# 方法一：百分比形式 (乘以 100) - 業界標準
df['BIAS_percent'] = ((df['price'] - df['MA']) / df['MA']) * 100

# 方法二：小數形式 (不乘以 100) - 原始比率
df['BIAS_decimal'] = (df['price'] - df['MA']) / df['MA']

# 4. 顯示結果
# 為了方便查看，我們移除包含 NaN 的初始行
results = df.dropna()
print("BIAS 指標計算結果比較：")
print(results[['price', 'MA', 'BIAS_decimal', 'BIAS_percent']].round(4))


# 5. 繪製圖表進行視覺化比較
# 創建一個包含兩個子圖的圖表
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
fig.suptitle('BIAS 指標兩種計算方式比較', fontsize=16)

# 圖一：股價與移動平均線
ax1.plot(df.index, df['price'], label='收盤價 (Price)', color='blue')
ax1.plot(df.index, df['MA'], label=f'{n}日移動平均線 (MA)', color='orange', linestyle='--')
ax1.set_title('股價與移動平均線')
ax1.set_ylabel('價格')
ax1.legend()
ax1.grid(True)

# 圖二：BIAS (百分比形式)
ax2.plot(results.index, results['BIAS_percent'], label='BIAS (%)', color='green')
ax2.axhline(0, color='gray', linestyle='--')
ax2.set_title('BIAS 指標 (百分比形式)')
ax2.set_ylabel('乖離率 (%)')
ax2.legend()
ax2.grid(True)

# 為了在同一個圖上顯示兩種刻度，我們創建一個共享X軸的第二個Y軸
ax3 = ax2.twinx()

# 設置第二個Y軸的標籤和範圍
# 範圍與第一個Y軸（百分比）同步，但數值是其1/100
y_min, y_max = ax2.get_ylim()
ax3.set_ylim(y_min / 100, y_max / 100)
ax3.set_ylabel('乖離率 (小數)')

# 顯示圖表
plt.xlabel('交易日')
plt.tight_layout(rect=(0, 0, 1, 0.96))
plt.show()

print("\n圖表說明：")
print("1. 上圖顯示了原始股價和其6日移動平均線。")
print("2. 下圖顯示了用百分比計算的BIAS指標線。")
print("3. 請注意，如果我們繪製小數形式的BIAS，線的形狀會完全一樣，只有Y軸的刻度會從 `5` 變成 `0.05`。")
