# MCP 工具快速参考指南

## 工具分类速查表

### 📁 文件系统工具 (1-20)
| 工具 | 功能 | 常用参数 |
|------|------|----------|
| `file_read` | 读取文件 | `path` |
| `file_write` | 写入文件 | `path, content` |
| `file_append` | 追加内容 | `path, content` |
| `file_delete` | 删除文件 | `path` |
| `file_copy` | 复制文件 | `source, destination` |
| `file_move` | 移动文件 | `source, destination` |
| `directory_create` | 创建目录 | `path` |
| `directory_list` | 列出目录 | `path, recursive?` |
| `file_search` | 搜索文件 | `pattern, directory` |
| `file_info` | 文件信息 | `path` |
| `file_permissions` | 修改权限 | `path, mode` |
| `file_compress` | 压缩文件 | `source, destination, format` |
| `file_extract` | 解压文件 | `archive, destination` |
| `file_watch` | 监控文件 | `path, events` |
| `file_backup` | 备份文件 | `source, backup_dir` |
| `file_sync` | 同步文件 | `source, destination` |
| `file_hash` | 计算哈希 | `path, algorithm` |
| `file_compare` | 比较文件 | `file1, file2` |
| `file_encrypt` | 加密文件 | `source, destination, key` |
| `file_decrypt` | 解密文件 | `source, destination, key` |

### 🌐 网络工具 (21-40)
| 工具 | 功能 | 常用参数 |
|------|------|----------|
| `http_get` | GET 请求 | `url, headers?` |
| `http_post` | POST 请求 | `url, data, headers?` |
| `http_put` | PUT 请求 | `url, data, headers?` |
| `http_delete` | DELETE 请求 | `url, headers?` |
| `websocket_connect` | WebSocket 连接 | `url, protocols?` |
| `websocket_send` | 发送 WS 消息 | `connection_id, message` |
| `ftp_connect` | FTP 连接 | `host, username, password` |
| `ftp_upload` | FTP 上传 | `connection_id, local_path, remote_path` |
| `ssh_connect` | SSH 连接 | `host, username, password?, key?` |
| `ssh_execute` | SSH 执行 | `connection_id, command` |
| `dns_lookup` | DNS 查询 | `domain, record_type?` |
| `ping` | 网络 ping | `host, count?` |
| `port_scan` | 端口扫描 | `host, ports` |
| `url_parse` | URL 解析 | `url` |
| `url_encode` | URL 编码 | `text` |
| `url_decode` | URL 解码 | `encoded_text` |
| `web_scrape` | 网页抓取 | `url, selector?` |
| `download_file` | 下载文件 | `url, destination` |
| `upload_file` | 上传文件 | `url, file_path` |
| `proxy_request` | 代理请求 | `url, proxy, method` |

### 🗄️ 数据库工具 (41-60)
| 工具 | 功能 | 常用参数 |
|------|------|----------|
| `db_connect` | 数据库连接 | `type, connection_string` |
| `db_query` | 执行查询 | `connection_id, query, parameters?` |
| `db_insert` | 插入数据 | `connection_id, table, data` |
| `db_update` | 更新数据 | `connection_id, table, data, where` |
| `db_delete` | 删除数据 | `connection_id, table, where` |
| `db_create_table` | 创建表 | `connection_id, table_name, schema` |
| `db_drop_table` | 删除表 | `connection_id, table_name` |
| `db_backup` | 备份数据库 | `connection_id, backup_path` |
| `db_restore` | 恢复数据库 | `connection_id, backup_path` |
| `db_migrate` | 数据库迁移 | `connection_id, migration_file` |
| `redis_connect` | Redis 连接 | `host, port, password?` |
| `redis_get` | Redis 获取 | `connection_id, key` |
| `redis_set` | Redis 设置 | `connection_id, key, value, ttl?` |
| `mongodb_connect` | MongoDB 连接 | `connection_string` |
| `mongodb_find` | MongoDB 查询 | `connection_id, collection, query` |
| `mongodb_insert` | MongoDB 插入 | `connection_id, collection, document` |
| `elasticsearch_connect` | ES 连接 | `host, port` |
| `elasticsearch_search` | ES 搜索 | `connection_id, index, query` |
| `db_transaction_begin` | 开始事务 | `connection_id` |
| `db_transaction_commit` | 提交事务 | `connection_id, transaction_id` |

### 🛠️ 开发工具 (61-80)
| 工具 | 功能 | 常用参数 |
|------|------|----------|
| `git_clone` | 克隆仓库 | `repository_url, destination` |
| `git_commit` | 提交代码 | `repository_path, message, files?` |
| `git_push` | 推送代码 | `repository_path, remote?, branch?` |
| `git_pull` | 拉取代码 | `repository_path, remote?, branch?` |
| `git_branch` | 创建分支 | `repository_path, branch_name` |
| `git_checkout` | 切换分支 | `repository_path, branch_name` |
| `git_merge` | 合并分支 | `repository_path, source_branch, target_branch` |
| `git_status` | 查看状态 | `repository_path` |
| `git_log` | 提交历史 | `repository_path, limit?` |
| `code_format` | 格式化代码 | `file_path, language` |
| `code_lint` | 代码检查 | `file_path, language, rules?` |
| `code_compile` | 编译代码 | `source_path, output_path, language` |
| `test_run` | 运行测试 | `test_path, framework` |
| `build_project` | 构建项目 | `project_path, build_tool` |
| `deploy_application` | 部署应用 | `source_path, target_environment` |
| `docker_build` | 构建镜像 | `dockerfile_path, image_name` |
| `docker_run` | 运行容器 | `image_name, container_name, options?` |
| `kubernetes_deploy` | K8s 部署 | `manifest_path, namespace?` |
| `npm_install` | 安装 npm 包 | `package_name, project_path` |
| `pip_install` | 安装 Python 包 | `package_name, virtual_env?` |

### ⚙️ 系统工具 (81-100)
| 工具 | 功能 | 常用参数 |
|------|------|----------|
| `process_list` | 列出进程 | `filter?` |
| `process_kill` | 终止进程 | `pid, signal?` |
| `process_start` | 启动进程 | `command, args?, cwd?` |
| `system_info` | 系统信息 | - |
| `memory_usage` | 内存使用 | - |
| `cpu_usage` | CPU 使用率 | - |
| `disk_usage` | 磁盘使用 | `path?` |
| `network_interfaces` | 网络接口 | - |
| `environment_get` | 获取环境变量 | `variable_name` |
| `environment_set` | 设置环境变量 | `variable_name, value` |
| `service_start` | 启动服务 | `service_name` |
| `service_stop` | 停止服务 | `service_name` |
| `service_status` | 服务状态 | `service_name` |
| `log_read` | 读取日志 | `log_file, lines?` |
| `log_watch` | 监控日志 | `log_file, pattern?` |
| `cron_add` | 添加定时任务 | `schedule, command` |
| `cron_list` | 列出定时任务 | - |
| `cron_remove` | 删除定时任务 | `job_id` |
| `backup_create` | 创建备份 | `source_path, backup_path` |
| `system_reboot` | 重启系统 | `delay?` |

## 常用工具组合

### 📋 文件操作流程
```json
// 1. 检查文件是否存在
{"tool": "file_info", "parameters": {"path": "/path/to/file.txt"}}

// 2. 备份原文件
{"tool": "file_backup", "parameters": {"source": "/path/to/file.txt", "backup_dir": "/backups"}}

// 3. 修改文件
{"tool": "file_write", "parameters": {"path": "/path/to/file.txt", "content": "new content"}}

// 4. 验证修改
{"tool": "file_read", "parameters": {"path": "/path/to/file.txt"}}
```

### 🌐 API 调用流程
```json
// 1. 获取认证令牌
{"tool": "http_post", "parameters": {"url": "https://api.example.com/auth", "data": {"username": "user", "password": "pass"}}}

// 2. 使用令牌调用 API
{"tool": "http_get", "parameters": {"url": "https://api.example.com/data", "headers": {"Authorization": "Bearer token"}}}

// 3. 处理响应数据
{"tool": "file_write", "parameters": {"path": "/tmp/api_response.json", "content": "response_data"}}
```

### 🗄️ 数据库操作流程
```json
// 1. 连接数据库
{"tool": "db_connect", "parameters": {"type": "postgresql", "connection_string": "postgresql://..."}}

// 2. 开始事务
{"tool": "db_transaction_begin", "parameters": {"connection_id": "conn_1"}}

// 3. 执行操作
{"tool": "db_insert", "parameters": {"connection_id": "conn_1", "table": "users", "data": {"name": "John"}}}

// 4. 提交事务
{"tool": "db_transaction_commit", "parameters": {"connection_id": "conn_1", "transaction_id": "tx_1"}}
```

### 🛠️ 代码部署流程
```json
// 1. 拉取最新代码
{"tool": "git_pull", "parameters": {"repository_path": "/app", "remote": "origin", "branch": "main"}}

// 2. 运行测试
{"tool": "test_run", "parameters": {"test_path": "/app/tests", "framework": "pytest"}}

// 3. 构建应用
{"tool": "build_project", "parameters": {"project_path": "/app", "build_tool": "npm"}}

// 4. 部署应用
{"tool": "deploy_application", "parameters": {"source_path": "/app", "target_environment": "production"}}
```

## 错误代码速查

| 错误代码 | 含义 | 解决方案 |
|----------|------|----------|
| `ENOENT` | 文件不存在 | 检查文件路径 |
| `EACCES` | 权限不足 | 检查文件权限 |
| `ECONNREFUSED` | 连接被拒绝 | 检查服务状态 |
| `ETIMEDOUT` | 连接超时 | 检查网络连接 |
| `ENOTFOUND` | 主机不存在 | 检查域名解析 |
| `EMFILE` | 文件描述符不足 | 增加系统限制 |
| `ENOSPC` | 磁盘空间不足 | 清理磁盘空间 |

## 性能优化提示

### 🚀 最佳实践
1. **批量操作**: 使用批量 API 减少网络开销
2. **缓存结果**: 缓存频繁访问的数据
3. **异步处理**: 使用异步工具提高并发性
4. **资源池**: 复用数据库连接和网络连接
5. **监控指标**: 定期监控工具性能

### ⚡ 常用优化技巧
- 使用 `file_search` 而不是 `directory_list` + 过滤
- 批量数据库操作使用事务
- 大文件操作使用流式处理
- 网络请求设置合适的超时时间
- 定期清理临时文件和缓存

---

*此快速参考指南提供了 MCP 工具的概览和常用模式，适合日常开发使用。*
