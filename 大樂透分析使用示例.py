#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大樂透統計分析使用示例
展示如何使用各種統計方法分析彩票數據
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 導入自定義分析工具
from 大樂透統計分析工具 import LotteryAnalyzer, create_sample_data

def main():
    """主函數 - 完整的分析流程示例"""
    
    print("🎯 大樂透統計分析系統")
    print("=" * 50)
    
    # 步驟1: 準備數據
    print("\n📊 步驟1: 準備分析數據")
    print("-" * 30)
    
    # 選項1: 使用示例數據
    print("使用示例數據進行分析...")
    data = create_sample_data(periods=500)  # 創建500期示例數據
    print(f"✅ 已生成 {len(data)} 期示例數據")
    
    # 選項2: 載入真實數據（如果有的話）
    # data = pd.read_csv('lottery_data.csv')
    
    # 步驟2: 初始化分析器
    print("\n🔧 步驟2: 初始化分析器")
    print("-" * 30)
    
    analyzer = LotteryAnalyzer()
    analyzer.load_data(data)
    
    # 步驟3: 執行基礎分析
    print("\n📈 步驟3: 執行統計分析")
    print("-" * 30)
    
    # 3.1 頻率分析
    print("\n🔢 頻率分析:")
    freq_results = analyzer.frequency_analysis()
    
    # 顯示熱門號碼
    hot_numbers = freq_results['hot_numbers']
    cold_numbers = freq_results['cold_numbers']
    print(f"🔥 熱門號碼: {hot_numbers}")
    print(f"❄️  冷門號碼: {cold_numbers}")
    
    # 3.2 分佈分析
    print("\n📊 分佈分析:")
    dist_results = analyzer.distribution_analysis()
    
    # 3.3 相關性分析
    print("\n🔗 相關性分析:")
    corr_results = analyzer.correlation_analysis()
    
    # 3.4 模式分析
    print("\n🎨 模式分析:")
    pattern_results = analyzer.pattern_analysis()
    
    # 3.5 統計檢驗
    print("\n🧪 統計檢驗:")
    test_results = analyzer.statistical_tests()
    
    # 步驟4: 進階分析示例
    print("\n🚀 步驟4: 進階分析示例")
    print("-" * 30)
    
    # 4.1 趨勢分析
    trend_analysis_example(data)
    
    # 4.2 週期性分析
    periodicity_analysis_example(data)
    
    # 4.3 預測示例
    prediction_example(data)
    
    # 步驟5: 生成報告
    print("\n📋 步驟5: 生成分析報告")
    print("-" * 30)
    
    generate_analysis_report(analyzer.analysis_results)
    
    # 步驟6: 實用建議
    print("\n💡 步驟6: 實用建議")
    print("-" * 30)
    
    provide_practical_suggestions(analyzer.analysis_results)
    
    print("\n✅ 分析完成！")
    print("📁 所有結果已保存在 analyzer.analysis_results 中")

def trend_analysis_example(data):
    """趨勢分析示例"""
    print("\n📈 趨勢分析示例:")
    
    # 計算和值趨勢
    sum_values = data[['number1', 'number2', 'number3', 'number4', 'number5', 'number6']].sum(axis=1)
    
    # 移動平均
    ma_10 = sum_values.rolling(window=10).mean()
    ma_20 = sum_values.rolling(window=20).mean()
    
    # 趨勢方向
    recent_trend = ma_10.iloc[-10:].mean() - ma_10.iloc[-20:-10].mean()
    trend_direction = "上升" if recent_trend > 0 else "下降" if recent_trend < 0 else "平穩"
    
    print(f"  📊 和值趨勢: {trend_direction}")
    print(f"  📊 最近10期平均和值: {ma_10.iloc[-1]:.1f}")
    print(f"  📊 最近20期平均和值: {ma_20.iloc[-1]:.1f}")

def periodicity_analysis_example(data):
    """週期性分析示例"""
    print("\n🔄 週期性分析示例:")
    
    # 分析號碼出現的週期性
    numbers_data = data[['number1', 'number2', 'number3', 'number4', 'number5', 'number6']]
    
    # 計算每個號碼的間隔期數
    intervals = {}
    for num in range(1, 50):
        appearances = []
        for idx, row in numbers_data.iterrows():
            if num in row.values:
                appearances.append(idx)
        
        if len(appearances) > 1:
            gaps = [appearances[i] - appearances[i-1] for i in range(1, len(appearances))]
            intervals[num] = {
                'avg_interval': np.mean(gaps) if gaps else 0,
                'max_interval': max(gaps) if gaps else 0,
                'current_gap': len(data) - max(appearances) if appearances else len(data)
            }
    
    # 找出當前遺漏最久的號碼
    max_gap_num = max(intervals.keys(), key=lambda x: intervals[x]['current_gap'])
    print(f"  ⏰ 當前遺漏最久號碼: {max_gap_num} (已遺漏 {intervals[max_gap_num]['current_gap']} 期)")
    
    # 找出平均間隔最短的號碼
    min_avg_interval_num = min(intervals.keys(), key=lambda x: intervals[x]['avg_interval'])
    print(f"  🔥 平均間隔最短號碼: {min_avg_interval_num} (平均間隔 {intervals[min_avg_interval_num]['avg_interval']:.1f} 期)")

def prediction_example(data):
    """預測示例"""
    print("\n🔮 預測示例:")
    
    # 基於頻率的簡單預測
    numbers_data = data[['number1', 'number2', 'number3', 'number4', 'number5', 'number6']]
    all_numbers = numbers_data.values.flatten()
    frequency = pd.Series(all_numbers).value_counts()
    
    # 預測策略1: 選擇頻率最高的號碼
    top_frequent = frequency.head(10).index.tolist()
    print(f"  📊 頻率預測法 - 推薦號碼: {sorted(top_frequent[:6])}")
    
    # 預測策略2: 平衡熱冷號碼
    hot_numbers = frequency.head(15).index.tolist()
    cold_numbers = frequency.tail(15).index.tolist()
    balanced_pick = sorted(np.random.choice(hot_numbers, 3, replace=False).tolist() + 
                          np.random.choice(cold_numbers, 3, replace=False).tolist())
    print(f"  ⚖️  平衡預測法 - 推薦號碼: {balanced_pick}")
    
    # 預測策略3: 基於遺漏值
    last_appearance = {}
    for num in range(1, 50):
        for idx in range(len(numbers_data)-1, -1, -1):
            if num in numbers_data.iloc[idx].values:
                last_appearance[num] = len(numbers_data) - 1 - idx
                break
        if num not in last_appearance:
            last_appearance[num] = len(numbers_data)
    
    # 選擇遺漏值適中的號碼
    sorted_by_gap = sorted(last_appearance.items(), key=lambda x: x[1])
    moderate_gap = [num for num, gap in sorted_by_gap[10:25]]  # 選擇遺漏值中等的號碼
    gap_pick = sorted(np.random.choice(moderate_gap, 6, replace=False))
    print(f"  ⏳ 遺漏值預測法 - 推薦號碼: {gap_pick}")

def generate_analysis_report(results):
    """生成分析報告"""
    print("\n📋 詳細分析報告:")
    
    if 'frequency' in results:
        freq_data = results['frequency']
        print(f"\n【頻率分析報告】")
        print(f"  • 號碼分佈變異係數: {freq_data['cv_frequency']:.3f}")
        print(f"  • 熱門號碼 ({len(freq_data['hot_numbers'])}個): {freq_data['hot_numbers']}")
        print(f"  • 冷門號碼 ({len(freq_data['cold_numbers'])}個): {freq_data['cold_numbers']}")
    
    if 'distribution' in results:
        dist_data = results['distribution']
        print(f"\n【分佈特徵報告】")
        print(f"  • 和值統計: 平均={dist_data['sum_statistics']['mean']:.1f}, "
              f"標準差={dist_data['sum_statistics']['std']:.1f}")
        print(f"  • 和值範圍: {dist_data['sum_statistics']['min']} - {dist_data['sum_statistics']['max']}")
        
        if 'odd_even_distribution' in dist_data:
            odd_even = dist_data['odd_even_distribution']
            print(f"  • 奇偶分佈: {dict(odd_even)}")
        
        if 'big_small_distribution' in dist_data:
            big_small = dist_data['big_small_distribution']
            print(f"  • 大小分佈: {dict(big_small)}")
    
    if 'statistical_tests' in results:
        test_data = results['statistical_tests']
        print(f"\n【統計檢驗報告】")
        
        if 'uniformity_test' in test_data:
            uniform_test = test_data['uniformity_test']
            print(f"  • 均勻性檢驗: χ²={uniform_test['chi2_statistic']:.2f}, "
                  f"p={uniform_test['p_value']:.4f}, "
                  f"結論={'符合均勻分佈' if uniform_test['is_uniform'] else '不符合均勻分佈'}")
        
        if 'normality_test' in test_data:
            normal_test = test_data['normality_test']
            print(f"  • 正態性檢驗: W={normal_test['shapiro_statistic']:.4f}, "
                  f"p={normal_test['p_value']:.4f}, "
                  f"結論={'符合正態分佈' if normal_test['is_normal'] else '不符合正態分佈'}")

def provide_practical_suggestions(results):
    """提供實用建議"""
    print("\n💡 實用建議:")
    
    print("\n🎯 選號策略建議:")
    print("  1. 📊 頻率均衡法: 結合熱門和冷門號碼")
    print("  2. ⏰ 週期輪換法: 關注遺漏值適中的號碼")
    print("  3. 🎨 模式多樣法: 避免過於規律的組合")
    print("  4. 📈 趨勢跟隨法: 參考近期和值趨勢")
    
    print("\n⚠️  重要提醒:")
    print("  • 統計分析僅供參考，不保證中獎")
    print("  • 彩票本質是隨機事件，過往數據不能預測未來")
    print("  • 理性投注，設定預算上限")
    print("  • 將彩票視為娛樂，不要依賴其獲利")
    
    print("\n📚 進一步學習:")
    print("  • 深入學習機率論和統計學")
    print("  • 了解隨機數生成原理")
    print("  • 研究行為經濟學和認知偏誤")
    print("  • 關注負責任博彩的相關資訊")

def create_visualization_examples():
    """創建視覺化示例"""
    print("\n📊 視覺化示例:")
    
    # 這裡可以添加各種圖表的創建代碼
    # 由於篇幅限制，僅提供框架
    
    print("  • 頻率分佈直方圖")
    print("  • 和值趨勢線圖")
    print("  • 相關性熱力圖")
    print("  • 奇偶分佈餅圖")
    print("  • 遺漏值散點圖")

if __name__ == "__main__":
    # 設置隨機種子以確保結果可重現
    np.random.seed(42)
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  分析被用戶中斷")
    except Exception as e:
        print(f"\n❌ 分析過程中發生錯誤: {e}")
        print("請檢查數據格式和依賴套件是否正確安裝")
    
    print("\n" + "=" * 50)
    print("感謝使用大樂透統計分析系統！")
    print("記住：理性分析，娛樂為主 🎲")
